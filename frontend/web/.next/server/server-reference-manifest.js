self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"7f4057fe8ce70dd95fafaf80e041384d2ce5e085fd\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fmemimal%2FPycharmProjects%2Fbetbet-platform%2Ffrontend%2Fweb%2Fnode_modules%2F.pnpm%2F%40clerk%2Bnextjs%406.30.1_next%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1__react-dom_93830ef75d57d8963e7374159a1b3431%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fkeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f4057fe8ce70dd95fafaf80e041384d2ce5e085fd%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227f48b3feba4659a368d321b94b36b2bb117f54a78f%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%2C%7B%22id%22%3A%227fc5e0d842bee4ed0100d97cd7dc477c7fb32cf614%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        },\n        \"app/dashboard/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fmemimal%2FPycharmProjects%2Fbetbet-platform%2Ffrontend%2Fweb%2Fnode_modules%2F.pnpm%2F%40clerk%2Bnextjs%406.30.1_next%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1__react-dom_93830ef75d57d8963e7374159a1b3431%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fkeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f4057fe8ce70dd95fafaf80e041384d2ce5e085fd%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227f48b3feba4659a368d321b94b36b2bb117f54a78f%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%2C%7B%22id%22%3A%227fc5e0d842bee4ed0100d97cd7dc477c7fb32cf614%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"rsc\",\n        \"app/dashboard/page\": \"rsc\"\n      }\n    },\n    \"7f48b3feba4659a368d321b94b36b2bb117f54a78f\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fmemimal%2FPycharmProjects%2Fbetbet-platform%2Ffrontend%2Fweb%2Fnode_modules%2F.pnpm%2F%40clerk%2Bnextjs%406.30.1_next%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1__react-dom_93830ef75d57d8963e7374159a1b3431%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fkeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f4057fe8ce70dd95fafaf80e041384d2ce5e085fd%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227f48b3feba4659a368d321b94b36b2bb117f54a78f%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%2C%7B%22id%22%3A%227fc5e0d842bee4ed0100d97cd7dc477c7fb32cf614%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        },\n        \"app/dashboard/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fmemimal%2FPycharmProjects%2Fbetbet-platform%2Ffrontend%2Fweb%2Fnode_modules%2F.pnpm%2F%40clerk%2Bnextjs%406.30.1_next%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1__react-dom_93830ef75d57d8963e7374159a1b3431%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fkeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f4057fe8ce70dd95fafaf80e041384d2ce5e085fd%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227f48b3feba4659a368d321b94b36b2bb117f54a78f%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%2C%7B%22id%22%3A%227fc5e0d842bee4ed0100d97cd7dc477c7fb32cf614%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"rsc\",\n        \"app/dashboard/page\": \"rsc\"\n      }\n    },\n    \"7fc5e0d842bee4ed0100d97cd7dc477c7fb32cf614\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fmemimal%2FPycharmProjects%2Fbetbet-platform%2Ffrontend%2Fweb%2Fnode_modules%2F.pnpm%2F%40clerk%2Bnextjs%406.30.1_next%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1__react-dom_93830ef75d57d8963e7374159a1b3431%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fkeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f4057fe8ce70dd95fafaf80e041384d2ce5e085fd%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227f48b3feba4659a368d321b94b36b2bb117f54a78f%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%2C%7B%22id%22%3A%227fc5e0d842bee4ed0100d97cd7dc477c7fb32cf614%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        },\n        \"app/dashboard/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fmemimal%2FPycharmProjects%2Fbetbet-platform%2Ffrontend%2Fweb%2Fnode_modules%2F.pnpm%2F%40clerk%2Bnextjs%406.30.1_next%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1__react-dom_93830ef75d57d8963e7374159a1b3431%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fkeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f4057fe8ce70dd95fafaf80e041384d2ce5e085fd%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227f48b3feba4659a368d321b94b36b2bb117f54a78f%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%2C%7B%22id%22%3A%227fc5e0d842bee4ed0100d97cd7dc477c7fb32cf614%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"rsc\",\n        \"app/dashboard/page\": \"rsc\"\n      }\n    },\n    \"7f4a752d347b7967e4949146702065264cdb6c4349\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fmemimal%2FPycharmProjects%2Fbetbet-platform%2Ffrontend%2Fweb%2Fnode_modules%2F.pnpm%2F%40clerk%2Bnextjs%406.30.1_next%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1__react-dom_93830ef75d57d8963e7374159a1b3431%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fserver-actions.js%22%2C%5B%7B%22id%22%3A%227f4a752d347b7967e4949146702065264cdb6c4349%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        },\n        \"app/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fmemimal%2FPycharmProjects%2Fbetbet-platform%2Ffrontend%2Fweb%2Fnode_modules%2F.pnpm%2F%40clerk%2Bnextjs%406.30.1_next%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1__react-dom_93830ef75d57d8963e7374159a1b3431%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fserver-actions.js%22%2C%5B%7B%22id%22%3A%227f4a752d347b7967e4949146702065264cdb6c4349%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/dashboard/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"