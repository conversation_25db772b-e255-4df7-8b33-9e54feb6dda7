"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1";
exports.ids = ["vendor-chunks/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-3664V5SS.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-3664V5SS.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIKeys: () => (/* binding */ APIKeys),\n/* harmony export */   CreateOrganization: () => (/* binding */ CreateOrganization),\n/* harmony export */   GoogleOneTap: () => (/* binding */ GoogleOneTap),\n/* harmony export */   OrganizationList: () => (/* binding */ OrganizationList),\n/* harmony export */   OrganizationProfile: () => (/* binding */ OrganizationProfile),\n/* harmony export */   OrganizationSwitcher: () => (/* binding */ OrganizationSwitcher),\n/* harmony export */   PricingTable: () => (/* binding */ PricingTable),\n/* harmony export */   SignIn: () => (/* binding */ SignIn),\n/* harmony export */   SignUp: () => (/* binding */ SignUp),\n/* harmony export */   TaskChooseOrganization: () => (/* binding */ TaskChooseOrganization),\n/* harmony export */   UserButton: () => (/* binding */ UserButton),\n/* harmony export */   UserProfile: () => (/* binding */ UserProfile),\n/* harmony export */   Waitlist: () => (/* binding */ Waitlist),\n/* harmony export */   assertSingleChild: () => (/* binding */ assertSingleChild),\n/* harmony export */   isConstructor: () => (/* binding */ isConstructor),\n/* harmony export */   normalizeWithDefaultValue: () => (/* binding */ normalizeWithDefaultValue),\n/* harmony export */   safeExecute: () => (/* binding */ safeExecute),\n/* harmony export */   withMaxAllowedInstancesGuard: () => (/* binding */ withMaxAllowedInstancesGuard)\n/* harmony export */ });\n/* harmony import */ var _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-KVSNHZPC.mjs */ \"(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-KVSNHZPC.mjs\");\n/* harmony import */ var _clerk_shared_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @clerk/shared/utils */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/utils/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _clerk_shared_object__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @clerk/shared/object */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/object.mjs\");\n/* harmony import */ var _clerk_shared_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @clerk/shared/react */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/react/index.mjs\");\n\n\n// src/components/uiComponents.tsx\n\n\n\n// src/utils/childrenUtils.tsx\n\nvar assertSingleChild = (children) => (name) => {\n  try {\n    return react__WEBPACK_IMPORTED_MODULE_2__.Children.only(children);\n  } catch {\n    return _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.errorThrower.throw((0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.multipleChildrenInButtonComponent)(name));\n  }\n};\nvar normalizeWithDefaultValue = (children, defaultText) => {\n  if (!children) {\n    children = defaultText;\n  }\n  if (typeof children === \"string\") {\n    children = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"button\", null, children);\n  }\n  return children;\n};\nvar safeExecute = (cb) => (...args) => {\n  if (cb && typeof cb === \"function\") {\n    return cb(...args);\n  }\n};\n\n// src/utils/isConstructor.ts\nfunction isConstructor(f) {\n  return typeof f === \"function\";\n}\n\n// src/utils/useMaxAllowedInstancesGuard.tsx\n\nvar counts = /* @__PURE__ */ new Map();\nfunction useMaxAllowedInstancesGuard(name, error, maxCount = 1) {\n  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(() => {\n    const count = counts.get(name) || 0;\n    if (count == maxCount) {\n      return _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.errorThrower.throw(error);\n    }\n    counts.set(name, count + 1);\n    return () => {\n      counts.set(name, (counts.get(name) || 1) - 1);\n    };\n  }, []);\n}\nfunction withMaxAllowedInstancesGuard(WrappedComponent, name, error) {\n  const displayName = WrappedComponent.displayName || WrappedComponent.name || name || \"Component\";\n  const Hoc = (props) => {\n    useMaxAllowedInstancesGuard(name, error);\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(WrappedComponent, { ...props });\n  };\n  Hoc.displayName = `withMaxAllowedInstancesGuard(${displayName})`;\n  return Hoc;\n}\n\n// src/utils/useCustomElementPortal.tsx\n\n\nvar useCustomElementPortal = (elements) => {\n  const [nodeMap, setNodeMap] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(/* @__PURE__ */ new Map());\n  return elements.map((el) => ({\n    id: el.id,\n    mount: (node) => setNodeMap((prev) => new Map(prev).set(String(el.id), node)),\n    unmount: () => setNodeMap((prev) => {\n      const newMap = new Map(prev);\n      newMap.set(String(el.id), null);\n      return newMap;\n    }),\n    portal: () => {\n      const node = nodeMap.get(String(el.id));\n      return node ? (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.createPortal)(el.component, node) : null;\n    }\n  }));\n};\n\n// src/utils/useCustomPages.tsx\n\n\n\n// src/utils/componentValidation.ts\n\nvar isThatComponent = (v, component) => {\n  return !!v && react__WEBPACK_IMPORTED_MODULE_2__.isValidElement(v) && (v == null ? void 0 : v.type) === component;\n};\n\n// src/utils/useCustomPages.tsx\nvar useUserProfileCustomPages = (children, options) => {\n  const reorderItemsLabels = [\"account\", \"security\"];\n  return useCustomPages(\n    {\n      children,\n      reorderItemsLabels,\n      LinkComponent: UserProfileLink,\n      PageComponent: UserProfilePage,\n      MenuItemsComponent: MenuItems,\n      componentName: \"UserProfile\"\n    },\n    options\n  );\n};\nvar useOrganizationProfileCustomPages = (children, options) => {\n  const reorderItemsLabels = [\"general\", \"members\"];\n  return useCustomPages(\n    {\n      children,\n      reorderItemsLabels,\n      LinkComponent: OrganizationProfileLink,\n      PageComponent: OrganizationProfilePage,\n      componentName: \"OrganizationProfile\"\n    },\n    options\n  );\n};\nvar useSanitizedChildren = (children) => {\n  const sanitizedChildren = [];\n  const excludedComponents = [\n    OrganizationProfileLink,\n    OrganizationProfilePage,\n    MenuItems,\n    UserProfilePage,\n    UserProfileLink\n  ];\n  react__WEBPACK_IMPORTED_MODULE_2__.Children.forEach(children, (child) => {\n    if (!excludedComponents.some((component) => isThatComponent(child, component))) {\n      sanitizedChildren.push(child);\n    }\n  });\n  return sanitizedChildren;\n};\nvar useCustomPages = (params, options) => {\n  const { children, LinkComponent, PageComponent, MenuItemsComponent, reorderItemsLabels, componentName } = params;\n  const { allowForAnyChildren = false } = options || {};\n  const validChildren = [];\n  react__WEBPACK_IMPORTED_MODULE_2__.Children.forEach(children, (child) => {\n    if (!isThatComponent(child, PageComponent) && !isThatComponent(child, LinkComponent) && !isThatComponent(child, MenuItemsComponent)) {\n      if (child && !allowForAnyChildren) {\n        (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_1__.logErrorInDevMode)((0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.customPagesIgnoredComponent)(componentName));\n      }\n      return;\n    }\n    const { props } = child;\n    const { children: children2, label, url, labelIcon } = props;\n    if (isThatComponent(child, PageComponent)) {\n      if (isReorderItem(props, reorderItemsLabels)) {\n        validChildren.push({ label });\n      } else if (isCustomPage(props)) {\n        validChildren.push({ label, labelIcon, children: children2, url });\n      } else {\n        (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_1__.logErrorInDevMode)((0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.customPageWrongProps)(componentName));\n        return;\n      }\n    }\n    if (isThatComponent(child, LinkComponent)) {\n      if (isExternalLink(props)) {\n        validChildren.push({ label, labelIcon, url });\n      } else {\n        (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_1__.logErrorInDevMode)((0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.customLinkWrongProps)(componentName));\n        return;\n      }\n    }\n  });\n  const customPageContents = [];\n  const customPageLabelIcons = [];\n  const customLinkLabelIcons = [];\n  validChildren.forEach((cp, index) => {\n    if (isCustomPage(cp)) {\n      customPageContents.push({ component: cp.children, id: index });\n      customPageLabelIcons.push({ component: cp.labelIcon, id: index });\n      return;\n    }\n    if (isExternalLink(cp)) {\n      customLinkLabelIcons.push({ component: cp.labelIcon, id: index });\n    }\n  });\n  const customPageContentsPortals = useCustomElementPortal(customPageContents);\n  const customPageLabelIconsPortals = useCustomElementPortal(customPageLabelIcons);\n  const customLinkLabelIconsPortals = useCustomElementPortal(customLinkLabelIcons);\n  const customPages = [];\n  const customPagesPortals = [];\n  validChildren.forEach((cp, index) => {\n    if (isReorderItem(cp, reorderItemsLabels)) {\n      customPages.push({ label: cp.label });\n      return;\n    }\n    if (isCustomPage(cp)) {\n      const {\n        portal: contentPortal,\n        mount,\n        unmount\n      } = customPageContentsPortals.find((p) => p.id === index);\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon\n      } = customPageLabelIconsPortals.find((p) => p.id === index);\n      customPages.push({ label: cp.label, url: cp.url, mount, unmount, mountIcon, unmountIcon });\n      customPagesPortals.push(contentPortal);\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n    if (isExternalLink(cp)) {\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon\n      } = customLinkLabelIconsPortals.find((p) => p.id === index);\n      customPages.push({ label: cp.label, url: cp.url, mountIcon, unmountIcon });\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n  });\n  return { customPages, customPagesPortals };\n};\nvar isReorderItem = (childProps, validItems) => {\n  const { children, label, url, labelIcon } = childProps;\n  return !children && !url && !labelIcon && validItems.some((v) => v === label);\n};\nvar isCustomPage = (childProps) => {\n  const { children, label, url, labelIcon } = childProps;\n  return !!children && !!url && !!labelIcon && !!label;\n};\nvar isExternalLink = (childProps) => {\n  const { children, label, url, labelIcon } = childProps;\n  return !children && !!url && !!labelIcon && !!label;\n};\n\n// src/utils/useCustomMenuItems.tsx\n\n\nvar useUserButtonCustomMenuItems = (children) => {\n  const reorderItemsLabels = [\"manageAccount\", \"signOut\"];\n  return useCustomMenuItems({\n    children,\n    reorderItemsLabels,\n    MenuItemsComponent: MenuItems,\n    MenuActionComponent: MenuAction,\n    MenuLinkComponent: MenuLink,\n    UserProfileLinkComponent: UserProfileLink,\n    UserProfilePageComponent: UserProfilePage\n  });\n};\nvar useCustomMenuItems = ({\n  children,\n  MenuItemsComponent,\n  MenuActionComponent,\n  MenuLinkComponent,\n  UserProfileLinkComponent,\n  UserProfilePageComponent,\n  reorderItemsLabels\n}) => {\n  const validChildren = [];\n  const customMenuItems = [];\n  const customMenuItemsPortals = [];\n  react__WEBPACK_IMPORTED_MODULE_2__.Children.forEach(children, (child) => {\n    if (!isThatComponent(child, MenuItemsComponent) && !isThatComponent(child, UserProfileLinkComponent) && !isThatComponent(child, UserProfilePageComponent)) {\n      if (child) {\n        (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_1__.logErrorInDevMode)(_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.userButtonIgnoredComponent);\n      }\n      return;\n    }\n    if (isThatComponent(child, UserProfileLinkComponent) || isThatComponent(child, UserProfilePageComponent)) {\n      return;\n    }\n    const { props } = child;\n    react__WEBPACK_IMPORTED_MODULE_2__.Children.forEach(props.children, (child2) => {\n      if (!isThatComponent(child2, MenuActionComponent) && !isThatComponent(child2, MenuLinkComponent)) {\n        if (child2) {\n          (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_1__.logErrorInDevMode)(_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.customMenuItemsIgnoredComponent);\n        }\n        return;\n      }\n      const { props: props2 } = child2;\n      const { label, labelIcon, href, onClick, open } = props2;\n      if (isThatComponent(child2, MenuActionComponent)) {\n        if (isReorderItem2(props2, reorderItemsLabels)) {\n          validChildren.push({ label });\n        } else if (isCustomMenuItem(props2)) {\n          const baseItem = {\n            label,\n            labelIcon\n          };\n          if (onClick !== void 0) {\n            validChildren.push({\n              ...baseItem,\n              onClick\n            });\n          } else if (open !== void 0) {\n            validChildren.push({\n              ...baseItem,\n              open: open.startsWith(\"/\") ? open : `/${open}`\n            });\n          } else {\n            (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_1__.logErrorInDevMode)(\"Custom menu item must have either onClick or open property\");\n            return;\n          }\n        } else {\n          (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_1__.logErrorInDevMode)(_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.userButtonMenuItemsActionWrongsProps);\n          return;\n        }\n      }\n      if (isThatComponent(child2, MenuLinkComponent)) {\n        if (isExternalLink2(props2)) {\n          validChildren.push({ label, labelIcon, href });\n        } else {\n          (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_1__.logErrorInDevMode)(_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.userButtonMenuItemLinkWrongProps);\n          return;\n        }\n      }\n    });\n  });\n  const customMenuItemLabelIcons = [];\n  const customLinkLabelIcons = [];\n  validChildren.forEach((mi, index) => {\n    if (isCustomMenuItem(mi)) {\n      customMenuItemLabelIcons.push({ component: mi.labelIcon, id: index });\n    }\n    if (isExternalLink2(mi)) {\n      customLinkLabelIcons.push({ component: mi.labelIcon, id: index });\n    }\n  });\n  const customMenuItemLabelIconsPortals = useCustomElementPortal(customMenuItemLabelIcons);\n  const customLinkLabelIconsPortals = useCustomElementPortal(customLinkLabelIcons);\n  validChildren.forEach((mi, index) => {\n    if (isReorderItem2(mi, reorderItemsLabels)) {\n      customMenuItems.push({\n        label: mi.label\n      });\n    }\n    if (isCustomMenuItem(mi)) {\n      const {\n        portal: iconPortal,\n        mount: mountIcon,\n        unmount: unmountIcon\n      } = customMenuItemLabelIconsPortals.find((p) => p.id === index);\n      const menuItem = {\n        label: mi.label,\n        mountIcon,\n        unmountIcon\n      };\n      if (\"onClick\" in mi) {\n        menuItem.onClick = mi.onClick;\n      } else if (\"open\" in mi) {\n        menuItem.open = mi.open;\n      }\n      customMenuItems.push(menuItem);\n      customMenuItemsPortals.push(iconPortal);\n    }\n    if (isExternalLink2(mi)) {\n      const {\n        portal: iconPortal,\n        mount: mountIcon,\n        unmount: unmountIcon\n      } = customLinkLabelIconsPortals.find((p) => p.id === index);\n      customMenuItems.push({\n        label: mi.label,\n        href: mi.href,\n        mountIcon,\n        unmountIcon\n      });\n      customMenuItemsPortals.push(iconPortal);\n    }\n  });\n  return { customMenuItems, customMenuItemsPortals };\n};\nvar isReorderItem2 = (childProps, validItems) => {\n  const { children, label, onClick, labelIcon } = childProps;\n  return !children && !onClick && !labelIcon && validItems.some((v) => v === label);\n};\nvar isCustomMenuItem = (childProps) => {\n  const { label, labelIcon, onClick, open } = childProps;\n  return !!labelIcon && !!label && (typeof onClick === \"function\" || typeof open === \"string\");\n};\nvar isExternalLink2 = (childProps) => {\n  const { label, href, labelIcon } = childProps;\n  return !!href && !!labelIcon && !!label;\n};\n\n// src/utils/useWaitForComponentMount.ts\n\nfunction waitForElementChildren(options) {\n  const { root = document == null ? void 0 : document.body, selector, timeout = 0 } = options;\n  return new Promise((resolve, reject) => {\n    if (!root) {\n      reject(new Error(\"No root element provided\"));\n      return;\n    }\n    let elementToWatch = root;\n    if (selector) {\n      elementToWatch = root == null ? void 0 : root.querySelector(selector);\n    }\n    const isElementAlreadyPresent = (elementToWatch == null ? void 0 : elementToWatch.childElementCount) && elementToWatch.childElementCount > 0;\n    if (isElementAlreadyPresent) {\n      resolve();\n      return;\n    }\n    const observer = new MutationObserver((mutationsList) => {\n      for (const mutation of mutationsList) {\n        if (mutation.type === \"childList\") {\n          if (!elementToWatch && selector) {\n            elementToWatch = root == null ? void 0 : root.querySelector(selector);\n          }\n          if ((elementToWatch == null ? void 0 : elementToWatch.childElementCount) && elementToWatch.childElementCount > 0) {\n            observer.disconnect();\n            resolve();\n            return;\n          }\n        }\n      }\n    });\n    observer.observe(root, { childList: true, subtree: true });\n    if (timeout > 0) {\n      setTimeout(() => {\n        observer.disconnect();\n        reject(new Error(`Timeout waiting for element children`));\n      }, timeout);\n    }\n  });\n}\nfunction useWaitForComponentMount(component) {\n  const watcherRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"rendering\");\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(() => {\n    if (!component) {\n      throw new Error(\"Clerk: no component name provided, unable to detect mount.\");\n    }\n    if (typeof window !== \"undefined\" && !watcherRef.current) {\n      watcherRef.current = waitForElementChildren({ selector: `[data-clerk-component=\"${component}\"]` }).then(() => {\n        setStatus(\"rendered\");\n      }).catch(() => {\n        setStatus(\"error\");\n      });\n    }\n  }, [component]);\n  return status;\n}\n\n// src/components/ClerkHostRenderer.tsx\n\n\n\nvar isMountProps = (props) => {\n  return \"mount\" in props;\n};\nvar isOpenProps = (props) => {\n  return \"open\" in props;\n};\nvar stripMenuItemIconHandlers = (menuItems) => {\n  return menuItems == null ? void 0 : menuItems.map(({ mountIcon, unmountIcon, ...rest }) => rest);\n};\nvar ClerkHostRenderer = class extends react__WEBPACK_IMPORTED_MODULE_2__.PureComponent {\n  constructor() {\n    super(...arguments);\n    this.rootRef = react__WEBPACK_IMPORTED_MODULE_2__.createRef();\n  }\n  componentDidUpdate(_prevProps) {\n    var _a, _b, _c, _d;\n    if (!isMountProps(_prevProps) || !isMountProps(this.props)) {\n      return;\n    }\n    const prevProps = (0,_clerk_shared_object__WEBPACK_IMPORTED_MODULE_4__.without)(_prevProps.props, \"customPages\", \"customMenuItems\", \"children\");\n    const newProps = (0,_clerk_shared_object__WEBPACK_IMPORTED_MODULE_4__.without)(this.props.props, \"customPages\", \"customMenuItems\", \"children\");\n    const customPagesChanged = ((_a = prevProps.customPages) == null ? void 0 : _a.length) !== ((_b = newProps.customPages) == null ? void 0 : _b.length);\n    const customMenuItemsChanged = ((_c = prevProps.customMenuItems) == null ? void 0 : _c.length) !== ((_d = newProps.customMenuItems) == null ? void 0 : _d.length);\n    const prevMenuItemsWithoutHandlers = stripMenuItemIconHandlers(_prevProps.props.customMenuItems);\n    const newMenuItemsWithoutHandlers = stripMenuItemIconHandlers(this.props.props.customMenuItems);\n    if (!(0,_clerk_shared_react__WEBPACK_IMPORTED_MODULE_5__.isDeeplyEqual)(prevProps, newProps) || !(0,_clerk_shared_react__WEBPACK_IMPORTED_MODULE_5__.isDeeplyEqual)(prevMenuItemsWithoutHandlers, newMenuItemsWithoutHandlers) || customPagesChanged || customMenuItemsChanged) {\n      if (this.rootRef.current) {\n        this.props.updateProps({ node: this.rootRef.current, props: this.props.props });\n      }\n    }\n  }\n  componentDidMount() {\n    if (this.rootRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.mount(this.rootRef.current, this.props.props);\n      }\n      if (isOpenProps(this.props)) {\n        this.props.open(this.props.props);\n      }\n    }\n  }\n  componentWillUnmount() {\n    if (this.rootRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.unmount(this.rootRef.current);\n      }\n      if (isOpenProps(this.props)) {\n        this.props.close();\n      }\n    }\n  }\n  render() {\n    const { hideRootHtmlElement = false } = this.props;\n    const rootAttributes = {\n      ref: this.rootRef,\n      ...this.props.rootProps,\n      ...this.props.component && { \"data-clerk-component\": this.props.component }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, !hideRootHtmlElement && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"div\", { ...rootAttributes }), this.props.children);\n  }\n};\n\n// src/components/uiComponents.tsx\nvar CustomPortalsRenderer = (props) => {\n  var _a, _b;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, (_a = props == null ? void 0 : props.customPagesPortals) == null ? void 0 : _a.map((portal, index) => (0,react__WEBPACK_IMPORTED_MODULE_2__.createElement)(portal, { key: index })), (_b = props == null ? void 0 : props.customMenuItemsPortals) == null ? void 0 : _b.map((portal, index) => (0,react__WEBPACK_IMPORTED_MODULE_2__.createElement)(portal, { key: index })));\n};\nvar SignIn = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, component, fallback, ...props }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountSignIn,\n        unmount: clerk.unmountSignIn,\n        updateProps: clerk.__unstable__updateProps,\n        props,\n        rootProps: rendererRootProps\n      }\n    ));\n  },\n  { component: \"SignIn\", renderWhileLoading: true }\n);\nvar SignUp = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, component, fallback, ...props }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountSignUp,\n        unmount: clerk.unmountSignUp,\n        updateProps: clerk.__unstable__updateProps,\n        props,\n        rootProps: rendererRootProps\n      }\n    ));\n  },\n  { component: \"SignUp\", renderWhileLoading: true }\n);\nfunction UserProfilePage({ children }) {\n  (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_1__.logErrorInDevMode)(_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.userProfilePageRenderedError);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, children);\n}\nfunction UserProfileLink({ children }) {\n  (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_1__.logErrorInDevMode)(_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.userProfileLinkRenderedError);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, children);\n}\nvar _UserProfile = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children);\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, shouldShowFallback && fallback, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountUserProfile,\n        unmount: clerk.unmountUserProfile,\n        updateProps: clerk.__unstable__updateProps,\n        props: { ...props, customPages },\n        rootProps: rendererRootProps\n      },\n      /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(CustomPortalsRenderer, { customPagesPortals })\n    ));\n  },\n  { component: \"UserProfile\", renderWhileLoading: true }\n);\nvar UserProfile = Object.assign(_UserProfile, {\n  Page: UserProfilePage,\n  Link: UserProfileLink\n});\nvar UserButtonContext = (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)({\n  mount: () => {\n  },\n  unmount: () => {\n  },\n  updateProps: () => {\n  }\n});\nvar _UserButton = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children, {\n      allowForAnyChildren: !!props.__experimental_asProvider\n    });\n    const userProfileProps = Object.assign(props.userProfileProps || {}, { customPages });\n    const { customMenuItems, customMenuItemsPortals } = useUserButtonCustomMenuItems(props.children);\n    const sanitizedChildren = useSanitizedChildren(props.children);\n    const passableProps = {\n      mount: clerk.mountUserButton,\n      unmount: clerk.unmountUserButton,\n      updateProps: clerk.__unstable__updateProps,\n      props: { ...props, userProfileProps, customMenuItems }\n    };\n    const portalProps = {\n      customPagesPortals,\n      customMenuItemsPortals\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(UserButtonContext.Provider, { value: passableProps }, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        ...passableProps,\n        hideRootHtmlElement: !!props.__experimental_asProvider,\n        rootProps: rendererRootProps\n      },\n      props.__experimental_asProvider ? sanitizedChildren : null,\n      /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(CustomPortalsRenderer, { ...portalProps })\n    ));\n  },\n  { component: \"UserButton\", renderWhileLoading: true }\n);\nfunction MenuItems({ children }) {\n  (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_1__.logErrorInDevMode)(_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.userButtonMenuItemsRenderedError);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, children);\n}\nfunction MenuAction({ children }) {\n  (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_1__.logErrorInDevMode)(_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.userButtonMenuActionRenderedError);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, children);\n}\nfunction MenuLink({ children }) {\n  (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_1__.logErrorInDevMode)(_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.userButtonMenuLinkRenderedError);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, children);\n}\nfunction UserButtonOutlet(outletProps) {\n  const providerProps = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(UserButtonContext);\n  const portalProps = {\n    ...providerProps,\n    props: {\n      ...providerProps.props,\n      ...outletProps\n    }\n  };\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(ClerkHostRenderer, { ...portalProps });\n}\nvar UserButton = Object.assign(_UserButton, {\n  UserProfilePage,\n  UserProfileLink,\n  MenuItems,\n  Action: MenuAction,\n  Link: MenuLink,\n  __experimental_Outlet: UserButtonOutlet\n});\nfunction OrganizationProfilePage({ children }) {\n  (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_1__.logErrorInDevMode)(_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.organizationProfilePageRenderedError);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, children);\n}\nfunction OrganizationProfileLink({ children }) {\n  (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_1__.logErrorInDevMode)(_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.organizationProfileLinkRenderedError);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, children);\n}\nvar _OrganizationProfile = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children);\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountOrganizationProfile,\n        unmount: clerk.unmountOrganizationProfile,\n        updateProps: clerk.__unstable__updateProps,\n        props: { ...props, customPages },\n        rootProps: rendererRootProps\n      },\n      /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(CustomPortalsRenderer, { customPagesPortals })\n    ));\n  },\n  { component: \"OrganizationProfile\", renderWhileLoading: true }\n);\nvar OrganizationProfile = Object.assign(_OrganizationProfile, {\n  Page: OrganizationProfilePage,\n  Link: OrganizationProfileLink\n});\nvar CreateOrganization = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, component, fallback, ...props }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountCreateOrganization,\n        unmount: clerk.unmountCreateOrganization,\n        updateProps: clerk.__unstable__updateProps,\n        props,\n        rootProps: rendererRootProps\n      }\n    ));\n  },\n  { component: \"CreateOrganization\", renderWhileLoading: true }\n);\nvar OrganizationSwitcherContext = (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)({\n  mount: () => {\n  },\n  unmount: () => {\n  },\n  updateProps: () => {\n  }\n});\nvar _OrganizationSwitcher = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children, {\n      allowForAnyChildren: !!props.__experimental_asProvider\n    });\n    const organizationProfileProps = Object.assign(props.organizationProfileProps || {}, { customPages });\n    const sanitizedChildren = useSanitizedChildren(props.children);\n    const passableProps = {\n      mount: clerk.mountOrganizationSwitcher,\n      unmount: clerk.unmountOrganizationSwitcher,\n      updateProps: clerk.__unstable__updateProps,\n      props: { ...props, organizationProfileProps },\n      rootProps: rendererRootProps,\n      component\n    };\n    clerk.__experimental_prefetchOrganizationSwitcher();\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(OrganizationSwitcherContext.Provider, { value: passableProps }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\n      ClerkHostRenderer,\n      {\n        ...passableProps,\n        hideRootHtmlElement: !!props.__experimental_asProvider\n      },\n      props.__experimental_asProvider ? sanitizedChildren : null,\n      /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(CustomPortalsRenderer, { customPagesPortals })\n    )));\n  },\n  { component: \"OrganizationSwitcher\", renderWhileLoading: true }\n);\nfunction OrganizationSwitcherOutlet(outletProps) {\n  const providerProps = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(OrganizationSwitcherContext);\n  const portalProps = {\n    ...providerProps,\n    props: {\n      ...providerProps.props,\n      ...outletProps\n    }\n  };\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(ClerkHostRenderer, { ...portalProps });\n}\nvar OrganizationSwitcher = Object.assign(_OrganizationSwitcher, {\n  OrganizationProfilePage,\n  OrganizationProfileLink,\n  __experimental_Outlet: OrganizationSwitcherOutlet\n});\nvar OrganizationList = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, component, fallback, ...props }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountOrganizationList,\n        unmount: clerk.unmountOrganizationList,\n        updateProps: clerk.__unstable__updateProps,\n        props,\n        rootProps: rendererRootProps\n      }\n    ));\n  },\n  { component: \"OrganizationList\", renderWhileLoading: true }\n);\nvar GoogleOneTap = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, component, fallback, ...props }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        open: clerk.openGoogleOneTap,\n        close: clerk.closeGoogleOneTap,\n        updateProps: clerk.__unstable__updateProps,\n        props,\n        rootProps: rendererRootProps\n      }\n    ));\n  },\n  { component: \"GoogleOneTap\", renderWhileLoading: true }\n);\nvar Waitlist = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, component, fallback, ...props }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountWaitlist,\n        unmount: clerk.unmountWaitlist,\n        updateProps: clerk.__unstable__updateProps,\n        props,\n        rootProps: rendererRootProps\n      }\n    ));\n  },\n  { component: \"Waitlist\", renderWhileLoading: true }\n);\nvar PricingTable = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, component, fallback, ...props }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountPricingTable,\n        unmount: clerk.unmountPricingTable,\n        updateProps: clerk.__unstable__updateProps,\n        props,\n        rootProps: rendererRootProps\n      }\n    ));\n  },\n  { component: \"PricingTable\", renderWhileLoading: true }\n);\nvar APIKeys = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, component, fallback, ...props }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountApiKeys,\n        unmount: clerk.unmountApiKeys,\n        updateProps: clerk.__unstable__updateProps,\n        props,\n        rootProps: rendererRootProps\n      }\n    ));\n  },\n  { component: \"ApiKeys\", renderWhileLoading: true }\n);\nvar TaskChooseOrganization = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, component, fallback, ...props }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountTaskChooseOrganization,\n        unmount: clerk.unmountTaskChooseOrganization,\n        updateProps: clerk.__unstable__updateProps,\n        props,\n        rootProps: rendererRootProps\n      }\n    ));\n  },\n  { component: \"TaskChooseOrganization\", renderWhileLoading: true }\n);\n\n\n//# sourceMappingURL=chunk-3664V5SS.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-3664V5SS.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-KVSNHZPC.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-KVSNHZPC.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* binding */ AuthContext),\n/* harmony export */   IsomorphicClerkContext: () => (/* binding */ IsomorphicClerkContext),\n/* harmony export */   __experimental_CheckoutProvider: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.__experimental_CheckoutProvider),\n/* harmony export */   __experimental_PaymentElement: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.__experimental_PaymentElement),\n/* harmony export */   __experimental_PaymentElementProvider: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.__experimental_PaymentElementProvider),\n/* harmony export */   __experimental_useCheckout: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.__experimental_useCheckout),\n/* harmony export */   __experimental_usePaymentElement: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.__experimental_usePaymentElement),\n/* harmony export */   customLinkWrongProps: () => (/* binding */ customLinkWrongProps),\n/* harmony export */   customMenuItemsIgnoredComponent: () => (/* binding */ customMenuItemsIgnoredComponent),\n/* harmony export */   customPageWrongProps: () => (/* binding */ customPageWrongProps),\n/* harmony export */   customPagesIgnoredComponent: () => (/* binding */ customPagesIgnoredComponent),\n/* harmony export */   errorThrower: () => (/* binding */ errorThrower),\n/* harmony export */   incompatibleRoutingWithPathProvidedError: () => (/* binding */ incompatibleRoutingWithPathProvidedError),\n/* harmony export */   multipleChildrenInButtonComponent: () => (/* binding */ multipleChildrenInButtonComponent),\n/* harmony export */   multipleClerkProvidersError: () => (/* binding */ multipleClerkProvidersError),\n/* harmony export */   noPathProvidedError: () => (/* binding */ noPathProvidedError),\n/* harmony export */   organizationProfileLinkRenderedError: () => (/* binding */ organizationProfileLinkRenderedError),\n/* harmony export */   organizationProfilePageRenderedError: () => (/* binding */ organizationProfilePageRenderedError),\n/* harmony export */   setErrorThrowerOptions: () => (/* binding */ setErrorThrowerOptions),\n/* harmony export */   unsupportedNonBrowserDomainOrProxyUrlFunction: () => (/* binding */ unsupportedNonBrowserDomainOrProxyUrlFunction),\n/* harmony export */   useAssertWrappedByClerkProvider: () => (/* binding */ useAssertWrappedByClerkProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useClerk: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useClerk),\n/* harmony export */   useDerivedAuth: () => (/* binding */ useDerivedAuth),\n/* harmony export */   useEmailLink: () => (/* binding */ useEmailLink),\n/* harmony export */   useIsomorphicClerkContext: () => (/* binding */ useIsomorphicClerkContext),\n/* harmony export */   useOrganization: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useOrganization),\n/* harmony export */   useOrganizationList: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useOrganizationList),\n/* harmony export */   useReverification: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useReverification),\n/* harmony export */   useSession: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useSession),\n/* harmony export */   useSessionList: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useSessionList),\n/* harmony export */   useSignIn: () => (/* binding */ useSignIn),\n/* harmony export */   useSignUp: () => (/* binding */ useSignUp),\n/* harmony export */   useUser: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useUser),\n/* harmony export */   userButtonIgnoredComponent: () => (/* binding */ userButtonIgnoredComponent),\n/* harmony export */   userButtonMenuActionRenderedError: () => (/* binding */ userButtonMenuActionRenderedError),\n/* harmony export */   userButtonMenuItemLinkWrongProps: () => (/* binding */ userButtonMenuItemLinkWrongProps),\n/* harmony export */   userButtonMenuItemsActionWrongsProps: () => (/* binding */ userButtonMenuItemsActionWrongsProps),\n/* harmony export */   userButtonMenuItemsRenderedError: () => (/* binding */ userButtonMenuItemsRenderedError),\n/* harmony export */   userButtonMenuLinkRenderedError: () => (/* binding */ userButtonMenuLinkRenderedError),\n/* harmony export */   userProfileLinkRenderedError: () => (/* binding */ userProfileLinkRenderedError),\n/* harmony export */   userProfilePageRenderedError: () => (/* binding */ userProfilePageRenderedError),\n/* harmony export */   withClerk: () => (/* binding */ withClerk)\n/* harmony export */ });\n/* harmony import */ var _clerk_shared_error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @clerk/shared/error */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/error.mjs\");\n/* harmony import */ var _clerk_shared_authorization__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @clerk/shared/authorization */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/authorization.mjs\");\n/* harmony import */ var _clerk_shared_telemetry__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @clerk/shared/telemetry */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/telemetry.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @clerk/shared/react */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/react/index.mjs\");\n// src/errors/errorThrower.ts\n\nvar errorThrower = (0,_clerk_shared_error__WEBPACK_IMPORTED_MODULE_0__.buildErrorThrower)({ packageName: \"@clerk/clerk-react\" });\nfunction setErrorThrowerOptions(options) {\n  errorThrower.setMessages(options).setPackageName(options);\n}\n\n// src/hooks/useAuth.ts\n\n\n\n\n// src/contexts/AuthContext.ts\n\nvar [AuthContext, useAuthContext] = (0,_clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.createContextAndHook)(\"AuthContext\");\n\n// src/contexts/IsomorphicClerkContext.tsx\n\nvar IsomorphicClerkContext = _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.ClerkInstanceContext;\nvar useIsomorphicClerkContext = _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useClerkInstanceContext;\n\n// src/errors/messages.ts\nvar multipleClerkProvidersError = \"You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.\";\nvar multipleChildrenInButtonComponent = (name) => `You've passed multiple children components to <${name}/>. You can only pass a single child component or text.`;\nvar invalidStateError = \"Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support\";\nvar unsupportedNonBrowserDomainOrProxyUrlFunction = \"Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.\";\nvar userProfilePageRenderedError = \"<UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.\";\nvar userProfileLinkRenderedError = \"<UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.\";\nvar organizationProfilePageRenderedError = \"<OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.\";\nvar organizationProfileLinkRenderedError = \"<OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.\";\nvar customPagesIgnoredComponent = (componentName) => `<${componentName} /> can only accept <${componentName}.Page /> and <${componentName}.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`;\nvar customPageWrongProps = (componentName) => `Missing props. <${componentName}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`;\nvar customLinkWrongProps = (componentName) => `Missing props. <${componentName}.Link /> component requires the following props: url, label and labelIcon.`;\nvar noPathProvidedError = (componentName) => `The <${componentName}/> component uses path-based routing by default unless a different routing strategy is provided using the \\`routing\\` prop. When path-based routing is used, you need to provide the path where the component is mounted on by using the \\`path\\` prop. Example: <${componentName} path={'/my-path'} />`;\nvar incompatibleRoutingWithPathProvidedError = (componentName) => `The \\`path\\` prop will only be respected when the Clerk component uses path-based routing. To resolve this error, pass \\`routing='path'\\` to the <${componentName}/> component, or drop the \\`path\\` prop to switch to hash-based routing. For more details please refer to our docs: https://clerk.com/docs`;\nvar userButtonIgnoredComponent = `<UserButton /> can only accept <UserButton.UserProfilePage />, <UserButton.UserProfileLink /> and <UserButton.MenuItems /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`;\nvar customMenuItemsIgnoredComponent = \"<UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.\";\nvar userButtonMenuItemsRenderedError = \"<UserButton.MenuItems /> component needs to be a direct child of `<UserButton />`.\";\nvar userButtonMenuActionRenderedError = \"<UserButton.Action /> component needs to be a direct child of `<UserButton.MenuItems />`.\";\nvar userButtonMenuLinkRenderedError = \"<UserButton.Link /> component needs to be a direct child of `<UserButton.MenuItems />`.\";\nvar userButtonMenuItemLinkWrongProps = \"Missing props. <UserButton.Link /> component requires the following props: href, label and labelIcon.\";\nvar userButtonMenuItemsActionWrongsProps = \"Missing props. <UserButton.Action /> component requires the following props: label.\";\n\n// src/hooks/useAssertWrappedByClerkProvider.ts\n\nvar useAssertWrappedByClerkProvider = (source) => {\n  (0,_clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useAssertWrappedByClerkProvider)(() => {\n    errorThrower.throwMissingClerkProviderError({ source });\n  });\n};\n\n// src/hooks/utils.ts\nvar clerkLoaded = (isomorphicClerk) => {\n  return new Promise((resolve) => {\n    const handler = (status) => {\n      if ([\"ready\", \"degraded\"].includes(status)) {\n        resolve();\n        isomorphicClerk.off(\"status\", handler);\n      }\n    };\n    isomorphicClerk.on(\"status\", handler, { notify: true });\n  });\n};\nvar createGetToken = (isomorphicClerk) => {\n  return async (options) => {\n    await clerkLoaded(isomorphicClerk);\n    if (!isomorphicClerk.session) {\n      return null;\n    }\n    return isomorphicClerk.session.getToken(options);\n  };\n};\nvar createSignOut = (isomorphicClerk) => {\n  return async (...args) => {\n    await clerkLoaded(isomorphicClerk);\n    return isomorphicClerk.signOut(...args);\n  };\n};\n\n// src/hooks/useAuth.ts\nvar useAuth = (initialAuthStateOrOptions = {}) => {\n  var _a;\n  useAssertWrappedByClerkProvider(\"useAuth\");\n  const { treatPendingAsSignedOut, ...rest } = initialAuthStateOrOptions != null ? initialAuthStateOrOptions : {};\n  const initialAuthState = rest;\n  const authContextFromHook = useAuthContext();\n  let authContext = authContextFromHook;\n  if (authContext.sessionId === void 0 && authContext.userId === void 0) {\n    authContext = initialAuthState != null ? initialAuthState : {};\n  }\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const getToken = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(createGetToken(isomorphicClerk), [isomorphicClerk]);\n  const signOut = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(createSignOut(isomorphicClerk), [isomorphicClerk]);\n  (_a = isomorphicClerk.telemetry) == null ? void 0 : _a.record((0,_clerk_shared_telemetry__WEBPACK_IMPORTED_MODULE_2__.eventMethodCalled)(\"useAuth\", { treatPendingAsSignedOut }));\n  return useDerivedAuth(\n    {\n      ...authContext,\n      getToken,\n      signOut\n    },\n    {\n      treatPendingAsSignedOut\n    }\n  );\n};\nfunction useDerivedAuth(authObject, { treatPendingAsSignedOut = true } = {}) {\n  const { userId, orgId, orgRole, has, signOut, getToken, orgPermissions, factorVerificationAge, sessionClaims } = authObject != null ? authObject : {};\n  const derivedHas = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(\n    (params) => {\n      if (has) {\n        return has(params);\n      }\n      return (0,_clerk_shared_authorization__WEBPACK_IMPORTED_MODULE_1__.createCheckAuthorization)({\n        userId,\n        orgId,\n        orgRole,\n        orgPermissions,\n        factorVerificationAge,\n        features: (sessionClaims == null ? void 0 : sessionClaims.fea) || \"\",\n        plans: (sessionClaims == null ? void 0 : sessionClaims.pla) || \"\"\n      })(params);\n    },\n    [has, userId, orgId, orgRole, orgPermissions, factorVerificationAge]\n  );\n  const payload = (0,_clerk_shared_authorization__WEBPACK_IMPORTED_MODULE_1__.resolveAuthState)({\n    authObject: {\n      ...authObject,\n      getToken,\n      signOut,\n      has: derivedHas\n    },\n    options: {\n      treatPendingAsSignedOut\n    }\n  });\n  if (!payload) {\n    return errorThrower.throw(invalidStateError);\n  }\n  return payload;\n}\n\n// src/hooks/useEmailLink.ts\n\nfunction useEmailLink(resource) {\n  const { startEmailLinkFlow, cancelEmailLinkFlow } = react__WEBPACK_IMPORTED_MODULE_3__.useMemo(() => resource.createEmailLinkFlow(), [resource]);\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(() => {\n    return cancelEmailLinkFlow;\n  }, []);\n  return {\n    startEmailLinkFlow,\n    cancelEmailLinkFlow\n  };\n}\n\n// src/hooks/useSignIn.ts\n\n\nvar useSignIn = () => {\n  var _a;\n  useAssertWrappedByClerkProvider(\"useSignIn\");\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const client = (0,_clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useClientContext)();\n  (_a = isomorphicClerk.telemetry) == null ? void 0 : _a.record((0,_clerk_shared_telemetry__WEBPACK_IMPORTED_MODULE_2__.eventMethodCalled)(\"useSignIn\"));\n  if (!client) {\n    return { isLoaded: false, signIn: void 0, setActive: void 0 };\n  }\n  return {\n    isLoaded: true,\n    signIn: client.signIn,\n    setActive: isomorphicClerk.setActive\n  };\n};\n\n// src/hooks/useSignUp.ts\n\n\nvar useSignUp = () => {\n  var _a;\n  useAssertWrappedByClerkProvider(\"useSignUp\");\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const client = (0,_clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useClientContext)();\n  (_a = isomorphicClerk.telemetry) == null ? void 0 : _a.record((0,_clerk_shared_telemetry__WEBPACK_IMPORTED_MODULE_2__.eventMethodCalled)(\"useSignUp\"));\n  if (!client) {\n    return { isLoaded: false, signUp: void 0, setActive: void 0 };\n  }\n  return {\n    isLoaded: true,\n    signUp: client.signUp,\n    setActive: isomorphicClerk.setActive\n  };\n};\n\n// src/hooks/index.ts\n\n\n// src/components/withClerk.tsx\n\nvar withClerk = (Component, displayNameOrOptions) => {\n  const passedDisplayedName = typeof displayNameOrOptions === \"string\" ? displayNameOrOptions : displayNameOrOptions == null ? void 0 : displayNameOrOptions.component;\n  const displayName = passedDisplayedName || Component.displayName || Component.name || \"Component\";\n  Component.displayName = displayName;\n  const options = typeof displayNameOrOptions === \"string\" ? void 0 : displayNameOrOptions;\n  const HOC = (props) => {\n    useAssertWrappedByClerkProvider(displayName || \"withClerk\");\n    const clerk = useIsomorphicClerkContext();\n    if (!clerk.loaded && !(options == null ? void 0 : options.renderWhileLoading)) {\n      return null;\n    }\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_3__.createElement(\n      Component,\n      {\n        ...props,\n        component: displayName,\n        clerk\n      }\n    );\n  };\n  HOC.displayName = `withClerk(${displayName})`;\n  return HOC;\n};\n\n\n//# sourceMappingURL=chunk-KVSNHZPC.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-KVSNHZPC.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-OANWQR3B.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-OANWQR3B.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __privateAdd: () => (/* binding */ __privateAdd),\n/* harmony export */   __privateGet: () => (/* binding */ __privateGet),\n/* harmony export */   __privateMethod: () => (/* binding */ __privateMethod),\n/* harmony export */   __privateSet: () => (/* binding */ __privateSet)\n/* harmony export */ });\nvar __typeError = (msg) => {\n  throw TypeError(msg);\n};\nvar __accessCheck = (obj, member, msg) => member.has(obj) || __typeError(\"Cannot \" + msg);\nvar __privateGet = (obj, member, getter) => (__accessCheck(obj, member, \"read from private field\"), getter ? getter.call(obj) : member.get(obj));\nvar __privateAdd = (obj, member, value) => member.has(obj) ? __typeError(\"Cannot add the same private member more than once\") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\nvar __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, \"write to private field\"), setter ? setter.call(obj, value) : member.set(obj, value), value);\nvar __privateMethod = (obj, member, method) => (__accessCheck(obj, member, \"access private method\"), method);\n\n\n//# sourceMappingURL=chunk-OANWQR3B.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNsZXJrK2NsZXJrLXJlYWN0QDUuNDEuMF9yZWFjdC1kb21AMTkuMS4xX3JlYWN0QDE5LjEuMV9fcmVhY3RAMTkuMS4xL25vZGVfbW9kdWxlcy9AY2xlcmsvY2xlcmstcmVhY3QvZGlzdC9jaHVuay1PQU5XUVIzQi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBT0U7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL21lbWltYWwvUHljaGFybVByb2plY3RzL2JldGJldC1wbGF0Zm9ybS9mcm9udGVuZC93ZWIvbm9kZV9tb2R1bGVzLy5wbnBtL0BjbGVyaytjbGVyay1yZWFjdEA1LjQxLjBfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvQGNsZXJrL2NsZXJrLXJlYWN0L2Rpc3QvY2h1bmstT0FOV1FSM0IubWpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBfX3R5cGVFcnJvciA9IChtc2cpID0+IHtcbiAgdGhyb3cgVHlwZUVycm9yKG1zZyk7XG59O1xudmFyIF9fYWNjZXNzQ2hlY2sgPSAob2JqLCBtZW1iZXIsIG1zZykgPT4gbWVtYmVyLmhhcyhvYmopIHx8IF9fdHlwZUVycm9yKFwiQ2Fubm90IFwiICsgbXNnKTtcbnZhciBfX3ByaXZhdGVHZXQgPSAob2JqLCBtZW1iZXIsIGdldHRlcikgPT4gKF9fYWNjZXNzQ2hlY2sob2JqLCBtZW1iZXIsIFwicmVhZCBmcm9tIHByaXZhdGUgZmllbGRcIiksIGdldHRlciA/IGdldHRlci5jYWxsKG9iaikgOiBtZW1iZXIuZ2V0KG9iaikpO1xudmFyIF9fcHJpdmF0ZUFkZCA9IChvYmosIG1lbWJlciwgdmFsdWUpID0+IG1lbWJlci5oYXMob2JqKSA/IF9fdHlwZUVycm9yKFwiQ2Fubm90IGFkZCB0aGUgc2FtZSBwcml2YXRlIG1lbWJlciBtb3JlIHRoYW4gb25jZVwiKSA6IG1lbWJlciBpbnN0YW5jZW9mIFdlYWtTZXQgPyBtZW1iZXIuYWRkKG9iaikgOiBtZW1iZXIuc2V0KG9iaiwgdmFsdWUpO1xudmFyIF9fcHJpdmF0ZVNldCA9IChvYmosIG1lbWJlciwgdmFsdWUsIHNldHRlcikgPT4gKF9fYWNjZXNzQ2hlY2sob2JqLCBtZW1iZXIsIFwid3JpdGUgdG8gcHJpdmF0ZSBmaWVsZFwiKSwgc2V0dGVyID8gc2V0dGVyLmNhbGwob2JqLCB2YWx1ZSkgOiBtZW1iZXIuc2V0KG9iaiwgdmFsdWUpLCB2YWx1ZSk7XG52YXIgX19wcml2YXRlTWV0aG9kID0gKG9iaiwgbWVtYmVyLCBtZXRob2QpID0+IChfX2FjY2Vzc0NoZWNrKG9iaiwgbWVtYmVyLCBcImFjY2VzcyBwcml2YXRlIG1ldGhvZFwiKSwgbWV0aG9kKTtcblxuZXhwb3J0IHtcbiAgX19wcml2YXRlR2V0LFxuICBfX3ByaXZhdGVBZGQsXG4gIF9fcHJpdmF0ZVNldCxcbiAgX19wcml2YXRlTWV0aG9kXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y2h1bmstT0FOV1FSM0IubWpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-OANWQR3B.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-WXC6AKVT.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-WXC6AKVT.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthenticateWithRedirectCallback: () => (/* binding */ AuthenticateWithRedirectCallback),\n/* harmony export */   ClerkDegraded: () => (/* binding */ ClerkDegraded),\n/* harmony export */   ClerkFailed: () => (/* binding */ ClerkFailed),\n/* harmony export */   ClerkLoaded: () => (/* binding */ ClerkLoaded),\n/* harmony export */   ClerkLoading: () => (/* binding */ ClerkLoading),\n/* harmony export */   MultisessionAppSupport: () => (/* binding */ MultisessionAppSupport),\n/* harmony export */   Protect: () => (/* binding */ Protect),\n/* harmony export */   RedirectToCreateOrganization: () => (/* binding */ RedirectToCreateOrganization),\n/* harmony export */   RedirectToOrganizationProfile: () => (/* binding */ RedirectToOrganizationProfile),\n/* harmony export */   RedirectToSignIn: () => (/* binding */ RedirectToSignIn),\n/* harmony export */   RedirectToSignUp: () => (/* binding */ RedirectToSignUp),\n/* harmony export */   RedirectToTask: () => (/* binding */ RedirectToTask),\n/* harmony export */   RedirectToUserProfile: () => (/* binding */ RedirectToUserProfile),\n/* harmony export */   SignedIn: () => (/* binding */ SignedIn),\n/* harmony export */   SignedOut: () => (/* binding */ SignedOut)\n/* harmony export */ });\n/* harmony import */ var _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-KVSNHZPC.mjs */ \"(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-KVSNHZPC.mjs\");\n/* harmony import */ var _clerk_shared_deprecated__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @clerk/shared/deprecated */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/deprecated.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _clerk_shared_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @clerk/shared/react */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/react/index.mjs\");\n\n\n// src/components/controlComponents.tsx\n\n\n\n// src/contexts/SessionContext.tsx\n\n\n// src/components/controlComponents.tsx\nvar SignedIn = ({ children, treatPendingAsSignedOut }) => {\n  (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.useAssertWrappedByClerkProvider)(\"SignedIn\");\n  const { userId } = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.useAuth)({ treatPendingAsSignedOut });\n  if (userId) {\n    return children;\n  }\n  return null;\n};\nvar SignedOut = ({ children, treatPendingAsSignedOut }) => {\n  (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.useAssertWrappedByClerkProvider)(\"SignedOut\");\n  const { userId } = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.useAuth)({ treatPendingAsSignedOut });\n  if (userId === null) {\n    return children;\n  }\n  return null;\n};\nvar ClerkLoaded = ({ children }) => {\n  (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.useAssertWrappedByClerkProvider)(\"ClerkLoaded\");\n  const isomorphicClerk = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicClerkContext)();\n  if (!isomorphicClerk.loaded) {\n    return null;\n  }\n  return children;\n};\nvar ClerkLoading = ({ children }) => {\n  (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.useAssertWrappedByClerkProvider)(\"ClerkLoading\");\n  const isomorphicClerk = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicClerkContext)();\n  if (isomorphicClerk.status !== \"loading\") {\n    return null;\n  }\n  return children;\n};\nvar ClerkFailed = ({ children }) => {\n  (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.useAssertWrappedByClerkProvider)(\"ClerkFailed\");\n  const isomorphicClerk = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicClerkContext)();\n  if (isomorphicClerk.status !== \"error\") {\n    return null;\n  }\n  return children;\n};\nvar ClerkDegraded = ({ children }) => {\n  (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.useAssertWrappedByClerkProvider)(\"ClerkDegraded\");\n  const isomorphicClerk = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicClerkContext)();\n  if (isomorphicClerk.status !== \"degraded\") {\n    return null;\n  }\n  return children;\n};\nvar Protect = ({ children, fallback, treatPendingAsSignedOut, ...restAuthorizedParams }) => {\n  (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.useAssertWrappedByClerkProvider)(\"Protect\");\n  const { isLoaded, has, userId } = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.useAuth)({ treatPendingAsSignedOut });\n  if (!isLoaded) {\n    return null;\n  }\n  const unauthorized = fallback != null ? fallback : null;\n  const authorized = children;\n  if (!userId) {\n    return unauthorized;\n  }\n  if (typeof restAuthorizedParams.condition === \"function\") {\n    if (restAuthorizedParams.condition(has)) {\n      return authorized;\n    }\n    return unauthorized;\n  }\n  if (restAuthorizedParams.role || restAuthorizedParams.permission || restAuthorizedParams.feature || restAuthorizedParams.plan) {\n    if (has(restAuthorizedParams)) {\n      return authorized;\n    }\n    return unauthorized;\n  }\n  return authorized;\n};\nvar RedirectToSignIn = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(({ clerk, ...props }) => {\n  const { client, session } = clerk;\n  const hasSignedInSessions = client.signedInSessions ? client.signedInSessions.length > 0 : (\n    // Compat for clerk-js<5.54.0 (which was released with the `signedInSessions` property)\n    client.activeSessions && client.activeSessions.length > 0\n  );\n  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(() => {\n    if (session === null && hasSignedInSessions) {\n      void clerk.redirectToAfterSignOut();\n    } else {\n      void clerk.redirectToSignIn(props);\n    }\n  }, []);\n  return null;\n}, \"RedirectToSignIn\");\nvar RedirectToSignUp = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(({ clerk, ...props }) => {\n  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(() => {\n    void clerk.redirectToSignUp(props);\n  }, []);\n  return null;\n}, \"RedirectToSignUp\");\nvar RedirectToTask = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(({ clerk }) => {\n  const { session } = clerk;\n  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(() => {\n    if (!session) {\n      void clerk.redirectToSignIn();\n      return;\n    }\n    void clerk.__internal_navigateToTaskIfAvailable();\n  }, []);\n  return null;\n}, \"RedirectToTask\");\nvar RedirectToUserProfile = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(({ clerk }) => {\n  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(() => {\n    (0,_clerk_shared_deprecated__WEBPACK_IMPORTED_MODULE_1__.deprecated)(\"RedirectToUserProfile\", \"Use the `redirectToUserProfile()` method instead.\");\n    void clerk.redirectToUserProfile();\n  }, []);\n  return null;\n}, \"RedirectToUserProfile\");\nvar RedirectToOrganizationProfile = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(({ clerk }) => {\n  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(() => {\n    (0,_clerk_shared_deprecated__WEBPACK_IMPORTED_MODULE_1__.deprecated)(\"RedirectToOrganizationProfile\", \"Use the `redirectToOrganizationProfile()` method instead.\");\n    void clerk.redirectToOrganizationProfile();\n  }, []);\n  return null;\n}, \"RedirectToOrganizationProfile\");\nvar RedirectToCreateOrganization = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(({ clerk }) => {\n  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(() => {\n    (0,_clerk_shared_deprecated__WEBPACK_IMPORTED_MODULE_1__.deprecated)(\"RedirectToCreateOrganization\", \"Use the `redirectToCreateOrganization()` method instead.\");\n    void clerk.redirectToCreateOrganization();\n  }, []);\n  return null;\n}, \"RedirectToCreateOrganization\");\nvar AuthenticateWithRedirectCallback = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, ...handleRedirectCallbackParams }) => {\n    react__WEBPACK_IMPORTED_MODULE_2__.useEffect(() => {\n      void clerk.handleRedirectCallback(handleRedirectCallbackParams);\n    }, []);\n    return null;\n  },\n  \"AuthenticateWithRedirectCallback\"\n);\nvar MultisessionAppSupport = ({ children }) => {\n  (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_0__.useAssertWrappedByClerkProvider)(\"MultisessionAppSupport\");\n  const session = (0,_clerk_shared_react__WEBPACK_IMPORTED_MODULE_3__.useSessionContext)();\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, { key: session ? session.id : \"no-users\" }, children);\n};\n\n\n//# sourceMappingURL=chunk-WXC6AKVT.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-WXC6AKVT.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/errors.mjs":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/errors.mjs ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailLinkErrorCode: () => (/* reexport safe */ _clerk_shared_error__WEBPACK_IMPORTED_MODULE_1__.EmailLinkErrorCode),\n/* harmony export */   EmailLinkErrorCodeStatus: () => (/* reexport safe */ _clerk_shared_error__WEBPACK_IMPORTED_MODULE_1__.EmailLinkErrorCodeStatus),\n/* harmony export */   isClerkAPIResponseError: () => (/* reexport safe */ _clerk_shared_error__WEBPACK_IMPORTED_MODULE_1__.isClerkAPIResponseError),\n/* harmony export */   isClerkRuntimeError: () => (/* reexport safe */ _clerk_shared_error__WEBPACK_IMPORTED_MODULE_1__.isClerkRuntimeError),\n/* harmony export */   isEmailLinkError: () => (/* reexport safe */ _clerk_shared_error__WEBPACK_IMPORTED_MODULE_1__.isEmailLinkError),\n/* harmony export */   isKnownError: () => (/* reexport safe */ _clerk_shared_error__WEBPACK_IMPORTED_MODULE_1__.isKnownError),\n/* harmony export */   isMetamaskError: () => (/* reexport safe */ _clerk_shared_error__WEBPACK_IMPORTED_MODULE_1__.isMetamaskError),\n/* harmony export */   isReverificationCancelledError: () => (/* reexport safe */ _clerk_shared_error__WEBPACK_IMPORTED_MODULE_1__.isReverificationCancelledError)\n/* harmony export */ });\n/* harmony import */ var _chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-OANWQR3B.mjs */ \"(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-OANWQR3B.mjs\");\n/* harmony import */ var _clerk_shared_error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @clerk/shared/error */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/error.mjs\");\n\n\n// src/errors.ts\n\n\n//# sourceMappingURL=errors.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNsZXJrK2NsZXJrLXJlYWN0QDUuNDEuMF9yZWFjdC1kb21AMTkuMS4xX3JlYWN0QDE5LjEuMV9fcmVhY3RAMTkuMS4xL25vZGVfbW9kdWxlcy9AY2xlcmsvY2xlcmstcmVhY3QvZGlzdC9lcnJvcnMubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7O0FBRTlCO0FBVTZCO0FBVTNCO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tZW1pbWFsL1B5Y2hhcm1Qcm9qZWN0cy9iZXRiZXQtcGxhdGZvcm0vZnJvbnRlbmQvd2ViL25vZGVfbW9kdWxlcy8ucG5wbS9AY2xlcmsrY2xlcmstcmVhY3RANS40MS4wX3JlYWN0LWRvbUAxOS4xLjFfcmVhY3RAMTkuMS4xX19yZWFjdEAxOS4xLjEvbm9kZV9tb2R1bGVzL0BjbGVyay9jbGVyay1yZWFjdC9kaXN0L2Vycm9ycy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFwiLi9jaHVuay1PQU5XUVIzQi5tanNcIjtcblxuLy8gc3JjL2Vycm9ycy50c1xuaW1wb3J0IHtcbiAgaXNDbGVya0FQSVJlc3BvbnNlRXJyb3IsXG4gIGlzQ2xlcmtSdW50aW1lRXJyb3IsXG4gIGlzRW1haWxMaW5rRXJyb3IsXG4gIGlzS25vd25FcnJvcixcbiAgaXNNZXRhbWFza0Vycm9yLFxuICBpc1JldmVyaWZpY2F0aW9uQ2FuY2VsbGVkRXJyb3IsXG4gIEVtYWlsTGlua0Vycm9yQ29kZSxcbiAgRW1haWxMaW5rRXJyb3JDb2RlU3RhdHVzXG59IGZyb20gXCJAY2xlcmsvc2hhcmVkL2Vycm9yXCI7XG5leHBvcnQge1xuICBFbWFpbExpbmtFcnJvckNvZGUsXG4gIEVtYWlsTGlua0Vycm9yQ29kZVN0YXR1cyxcbiAgaXNDbGVya0FQSVJlc3BvbnNlRXJyb3IsXG4gIGlzQ2xlcmtSdW50aW1lRXJyb3IsXG4gIGlzRW1haWxMaW5rRXJyb3IsXG4gIGlzS25vd25FcnJvcixcbiAgaXNNZXRhbWFza0Vycm9yLFxuICBpc1JldmVyaWZpY2F0aW9uQ2FuY2VsbGVkRXJyb3Jcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1lcnJvcnMubWpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/errors.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIKeys: () => (/* reexport safe */ _chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.APIKeys),\n/* harmony export */   AuthenticateWithRedirectCallback: () => (/* reexport safe */ _chunk_WXC6AKVT_mjs__WEBPACK_IMPORTED_MODULE_0__.AuthenticateWithRedirectCallback),\n/* harmony export */   ClerkDegraded: () => (/* reexport safe */ _chunk_WXC6AKVT_mjs__WEBPACK_IMPORTED_MODULE_0__.ClerkDegraded),\n/* harmony export */   ClerkFailed: () => (/* reexport safe */ _chunk_WXC6AKVT_mjs__WEBPACK_IMPORTED_MODULE_0__.ClerkFailed),\n/* harmony export */   ClerkLoaded: () => (/* reexport safe */ _chunk_WXC6AKVT_mjs__WEBPACK_IMPORTED_MODULE_0__.ClerkLoaded),\n/* harmony export */   ClerkLoading: () => (/* reexport safe */ _chunk_WXC6AKVT_mjs__WEBPACK_IMPORTED_MODULE_0__.ClerkLoading),\n/* harmony export */   ClerkProvider: () => (/* binding */ ClerkProvider),\n/* harmony export */   CreateOrganization: () => (/* reexport safe */ _chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.CreateOrganization),\n/* harmony export */   GoogleOneTap: () => (/* reexport safe */ _chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.GoogleOneTap),\n/* harmony export */   OrganizationList: () => (/* reexport safe */ _chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.OrganizationList),\n/* harmony export */   OrganizationProfile: () => (/* reexport safe */ _chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.OrganizationProfile),\n/* harmony export */   OrganizationSwitcher: () => (/* reexport safe */ _chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.OrganizationSwitcher),\n/* harmony export */   PricingTable: () => (/* reexport safe */ _chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.PricingTable),\n/* harmony export */   Protect: () => (/* reexport safe */ _chunk_WXC6AKVT_mjs__WEBPACK_IMPORTED_MODULE_0__.Protect),\n/* harmony export */   RedirectToCreateOrganization: () => (/* reexport safe */ _chunk_WXC6AKVT_mjs__WEBPACK_IMPORTED_MODULE_0__.RedirectToCreateOrganization),\n/* harmony export */   RedirectToOrganizationProfile: () => (/* reexport safe */ _chunk_WXC6AKVT_mjs__WEBPACK_IMPORTED_MODULE_0__.RedirectToOrganizationProfile),\n/* harmony export */   RedirectToSignIn: () => (/* reexport safe */ _chunk_WXC6AKVT_mjs__WEBPACK_IMPORTED_MODULE_0__.RedirectToSignIn),\n/* harmony export */   RedirectToSignUp: () => (/* reexport safe */ _chunk_WXC6AKVT_mjs__WEBPACK_IMPORTED_MODULE_0__.RedirectToSignUp),\n/* harmony export */   RedirectToTask: () => (/* reexport safe */ _chunk_WXC6AKVT_mjs__WEBPACK_IMPORTED_MODULE_0__.RedirectToTask),\n/* harmony export */   RedirectToUserProfile: () => (/* reexport safe */ _chunk_WXC6AKVT_mjs__WEBPACK_IMPORTED_MODULE_0__.RedirectToUserProfile),\n/* harmony export */   SignIn: () => (/* reexport safe */ _chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.SignIn),\n/* harmony export */   SignInButton: () => (/* binding */ SignInButton),\n/* harmony export */   SignInWithMetamaskButton: () => (/* binding */ SignInWithMetamaskButton),\n/* harmony export */   SignOutButton: () => (/* binding */ SignOutButton),\n/* harmony export */   SignUp: () => (/* reexport safe */ _chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.SignUp),\n/* harmony export */   SignUpButton: () => (/* binding */ SignUpButton),\n/* harmony export */   SignedIn: () => (/* reexport safe */ _chunk_WXC6AKVT_mjs__WEBPACK_IMPORTED_MODULE_0__.SignedIn),\n/* harmony export */   SignedOut: () => (/* reexport safe */ _chunk_WXC6AKVT_mjs__WEBPACK_IMPORTED_MODULE_0__.SignedOut),\n/* harmony export */   TaskChooseOrganization: () => (/* reexport safe */ _chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.TaskChooseOrganization),\n/* harmony export */   UserButton: () => (/* reexport safe */ _chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.UserButton),\n/* harmony export */   UserProfile: () => (/* reexport safe */ _chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.UserProfile),\n/* harmony export */   Waitlist: () => (/* reexport safe */ _chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.Waitlist),\n/* harmony export */   __experimental_CheckoutProvider: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.__experimental_CheckoutProvider),\n/* harmony export */   __experimental_PaymentElement: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.__experimental_PaymentElement),\n/* harmony export */   __experimental_PaymentElementProvider: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.__experimental_PaymentElementProvider),\n/* harmony export */   __experimental_useCheckout: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.__experimental_useCheckout),\n/* harmony export */   __experimental_usePaymentElement: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.__experimental_usePaymentElement),\n/* harmony export */   useAuth: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.useAuth),\n/* harmony export */   useClerk: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.useClerk),\n/* harmony export */   useEmailLink: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.useEmailLink),\n/* harmony export */   useOrganization: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.useOrganization),\n/* harmony export */   useOrganizationList: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.useOrganizationList),\n/* harmony export */   useReverification: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.useReverification),\n/* harmony export */   useSession: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.useSession),\n/* harmony export */   useSessionList: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.useSessionList),\n/* harmony export */   useSignIn: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.useSignIn),\n/* harmony export */   useSignUp: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.useSignUp),\n/* harmony export */   useUser: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.useUser)\n/* harmony export */ });\n/* harmony import */ var _chunk_WXC6AKVT_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-WXC6AKVT.mjs */ \"(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-WXC6AKVT.mjs\");\n/* harmony import */ var _chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-3664V5SS.mjs */ \"(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-3664V5SS.mjs\");\n/* harmony import */ var _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-KVSNHZPC.mjs */ \"(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-KVSNHZPC.mjs\");\n/* harmony import */ var _chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-OANWQR3B.mjs */ \"(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-OANWQR3B.mjs\");\n/* harmony import */ var _clerk_shared_loadClerkJsScript__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @clerk/shared/loadClerkJsScript */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/loadClerkJsScript.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _clerk_shared_keys__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @clerk/shared/keys */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/keys.mjs\");\n/* harmony import */ var _clerk_shared_deriveState__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @clerk/shared/deriveState */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/deriveState.mjs\");\n/* harmony import */ var _clerk_shared_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @clerk/shared/react */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/react/index.mjs\");\n/* harmony import */ var _clerk_shared_browser__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @clerk/shared/browser */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/browser.mjs\");\n/* harmony import */ var _clerk_shared_clerkEventBus__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @clerk/shared/clerkEventBus */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/clerkEventBus.mjs\");\n/* harmony import */ var _clerk_shared_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @clerk/shared/utils */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/utils/index.mjs\");\n\n\n\n\n\n// src/polyfills.ts\nif (typeof window !== \"undefined\" && !window.global) {\n  window.global = typeof global === \"undefined\" ? window : global;\n}\n\n// src/index.ts\n\n\n// src/components/SignInButton.tsx\n\nvar SignInButton = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.withClerk)(\n  ({ clerk, children, ...props }) => {\n    const {\n      signUpFallbackRedirectUrl,\n      forceRedirectUrl,\n      fallbackRedirectUrl,\n      signUpForceRedirectUrl,\n      mode,\n      initialValues,\n      withSignUp,\n      oauthFlow,\n      ...rest\n    } = props;\n    children = (0,_chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.normalizeWithDefaultValue)(children, \"Sign in\");\n    const child = (0,_chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.assertSingleChild)(children)(\"SignInButton\");\n    const clickHandler = () => {\n      const opts = {\n        forceRedirectUrl,\n        fallbackRedirectUrl,\n        signUpFallbackRedirectUrl,\n        signUpForceRedirectUrl,\n        initialValues,\n        withSignUp,\n        oauthFlow\n      };\n      if (mode === \"modal\") {\n        return clerk.openSignIn({ ...opts, appearance: props.appearance });\n      }\n      return clerk.redirectToSignIn({\n        ...opts,\n        signInFallbackRedirectUrl: fallbackRedirectUrl,\n        signInForceRedirectUrl: forceRedirectUrl\n      });\n    };\n    const wrappedChildClickHandler = async (e) => {\n      if (child && typeof child === \"object\" && \"props\" in child) {\n        await (0,_chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.safeExecute)(child.props.onClick)(e);\n      }\n      return clickHandler();\n    };\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return react__WEBPACK_IMPORTED_MODULE_5__.cloneElement(child, childProps);\n  },\n  { component: \"SignInButton\", renderWhileLoading: true }\n);\n\n// src/components/SignInWithMetamaskButton.tsx\n\nvar SignInWithMetamaskButton = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.withClerk)(\n  ({ clerk, children, ...props }) => {\n    const { redirectUrl, ...rest } = props;\n    children = (0,_chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.normalizeWithDefaultValue)(children, \"Sign in with Metamask\");\n    const child = (0,_chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.assertSingleChild)(children)(\"SignInWithMetamaskButton\");\n    const clickHandler = async () => {\n      async function authenticate() {\n        await clerk.authenticateWithMetamask({ redirectUrl: redirectUrl || void 0 });\n      }\n      void authenticate();\n    };\n    const wrappedChildClickHandler = async (e) => {\n      await (0,_chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.safeExecute)(child.props.onClick)(e);\n      return clickHandler();\n    };\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return react__WEBPACK_IMPORTED_MODULE_5__.cloneElement(child, childProps);\n  },\n  { component: \"SignInWithMetamask\", renderWhileLoading: true }\n);\n\n// src/components/SignOutButton.tsx\n\nvar SignOutButton = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.withClerk)(\n  ({ clerk, children, ...props }) => {\n    const { redirectUrl = \"/\", signOutOptions, ...rest } = props;\n    children = (0,_chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.normalizeWithDefaultValue)(children, \"Sign out\");\n    const child = (0,_chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.assertSingleChild)(children)(\"SignOutButton\");\n    const clickHandler = () => clerk.signOut({ redirectUrl, ...signOutOptions });\n    const wrappedChildClickHandler = async (e) => {\n      await (0,_chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.safeExecute)(child.props.onClick)(e);\n      return clickHandler();\n    };\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return react__WEBPACK_IMPORTED_MODULE_5__.cloneElement(child, childProps);\n  },\n  { component: \"SignOutButton\", renderWhileLoading: true }\n);\n\n// src/components/SignUpButton.tsx\n\nvar SignUpButton = (0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.withClerk)(\n  ({ clerk, children, ...props }) => {\n    const {\n      fallbackRedirectUrl,\n      forceRedirectUrl,\n      signInFallbackRedirectUrl,\n      signInForceRedirectUrl,\n      mode,\n      initialValues,\n      oauthFlow,\n      ...rest\n    } = props;\n    children = (0,_chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.normalizeWithDefaultValue)(children, \"Sign up\");\n    const child = (0,_chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.assertSingleChild)(children)(\"SignUpButton\");\n    const clickHandler = () => {\n      const opts = {\n        fallbackRedirectUrl,\n        forceRedirectUrl,\n        signInFallbackRedirectUrl,\n        signInForceRedirectUrl,\n        initialValues,\n        oauthFlow\n      };\n      if (mode === \"modal\") {\n        return clerk.openSignUp({\n          ...opts,\n          appearance: props.appearance,\n          unsafeMetadata: props.unsafeMetadata\n        });\n      }\n      return clerk.redirectToSignUp({\n        ...opts,\n        signUpFallbackRedirectUrl: fallbackRedirectUrl,\n        signUpForceRedirectUrl: forceRedirectUrl\n      });\n    };\n    const wrappedChildClickHandler = async (e) => {\n      if (child && typeof child === \"object\" && \"props\" in child) {\n        await (0,_chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.safeExecute)(child.props.onClick)(e);\n      }\n      return clickHandler();\n    };\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return react__WEBPACK_IMPORTED_MODULE_5__.cloneElement(child, childProps);\n  },\n  { component: \"SignUpButton\", renderWhileLoading: true }\n);\n\n// src/contexts/ClerkProvider.tsx\n\n\n\n// src/contexts/ClerkContextProvider.tsx\n\n\n\n\n// src/isomorphicClerk.ts\n\n\n\n\nif (typeof globalThis.__BUILD_DISABLE_RHC__ === \"undefined\") {\n  globalThis.__BUILD_DISABLE_RHC__ = false;\n}\nvar SDK_METADATA = {\n  name: \"@clerk/clerk-react\",\n  version: \"5.41.0\",\n  environment: \"development\"\n};\nvar _status, _domain, _proxyUrl, _publishableKey, _eventBus, _instance, _IsomorphicClerk_instances, waitForClerkJS_fn;\nvar _IsomorphicClerk = class _IsomorphicClerk {\n  constructor(options) {\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateAdd)(this, _IsomorphicClerk_instances);\n    this.clerkjs = null;\n    this.preopenOneTap = null;\n    this.preopenUserVerification = null;\n    this.preopenSignIn = null;\n    this.preopenCheckout = null;\n    this.preopenPlanDetails = null;\n    this.preopenSubscriptionDetails = null;\n    this.preopenSignUp = null;\n    this.preopenUserProfile = null;\n    this.preopenOrganizationProfile = null;\n    this.preopenCreateOrganization = null;\n    this.preOpenWaitlist = null;\n    this.premountSignInNodes = /* @__PURE__ */ new Map();\n    this.premountSignUpNodes = /* @__PURE__ */ new Map();\n    this.premountUserProfileNodes = /* @__PURE__ */ new Map();\n    this.premountUserButtonNodes = /* @__PURE__ */ new Map();\n    this.premountOrganizationProfileNodes = /* @__PURE__ */ new Map();\n    this.premountCreateOrganizationNodes = /* @__PURE__ */ new Map();\n    this.premountOrganizationSwitcherNodes = /* @__PURE__ */ new Map();\n    this.premountOrganizationListNodes = /* @__PURE__ */ new Map();\n    this.premountMethodCalls = /* @__PURE__ */ new Map();\n    this.premountWaitlistNodes = /* @__PURE__ */ new Map();\n    this.premountPricingTableNodes = /* @__PURE__ */ new Map();\n    this.premountApiKeysNodes = /* @__PURE__ */ new Map();\n    this.premountOAuthConsentNodes = /* @__PURE__ */ new Map();\n    this.premountTaskChooseOrganizationNodes = /* @__PURE__ */ new Map();\n    // A separate Map of `addListener` method calls to handle multiple listeners.\n    this.premountAddListenerCalls = /* @__PURE__ */ new Map();\n    this.loadedListeners = [];\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateAdd)(this, _status, \"loading\");\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateAdd)(this, _domain);\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateAdd)(this, _proxyUrl);\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateAdd)(this, _publishableKey);\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateAdd)(this, _eventBus, (0,_clerk_shared_clerkEventBus__WEBPACK_IMPORTED_MODULE_10__.createClerkEventBus)());\n    this.buildSignInUrl = (opts) => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildSignInUrl(opts)) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildSignInUrl\", callback);\n      }\n    };\n    this.buildSignUpUrl = (opts) => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildSignUpUrl(opts)) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildSignUpUrl\", callback);\n      }\n    };\n    this.buildAfterSignInUrl = (...args) => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildAfterSignInUrl(...args)) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildAfterSignInUrl\", callback);\n      }\n    };\n    this.buildAfterSignUpUrl = (...args) => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildAfterSignUpUrl(...args)) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildAfterSignUpUrl\", callback);\n      }\n    };\n    this.buildAfterSignOutUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildAfterSignOutUrl()) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildAfterSignOutUrl\", callback);\n      }\n    };\n    this.buildNewSubscriptionRedirectUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildNewSubscriptionRedirectUrl()) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildNewSubscriptionRedirectUrl\", callback);\n      }\n    };\n    this.buildAfterMultiSessionSingleSignOutUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildAfterMultiSessionSingleSignOutUrl()) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildAfterMultiSessionSingleSignOutUrl\", callback);\n      }\n    };\n    this.buildUserProfileUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildUserProfileUrl()) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildUserProfileUrl\", callback);\n      }\n    };\n    this.buildCreateOrganizationUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildCreateOrganizationUrl()) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildCreateOrganizationUrl\", callback);\n      }\n    };\n    this.buildOrganizationProfileUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildOrganizationProfileUrl()) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildOrganizationProfileUrl\", callback);\n      }\n    };\n    this.buildWaitlistUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildWaitlistUrl()) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildWaitlistUrl\", callback);\n      }\n    };\n    this.buildUrlWithAuth = (to) => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildUrlWithAuth(to)) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildUrlWithAuth\", callback);\n      }\n    };\n    this.handleUnauthenticated = async () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.handleUnauthenticated();\n      };\n      if (this.clerkjs && this.loaded) {\n        void callback();\n      } else {\n        this.premountMethodCalls.set(\"handleUnauthenticated\", callback);\n      }\n    };\n    this.on = (...args) => {\n      var _a;\n      if ((_a = this.clerkjs) == null ? void 0 : _a.on) {\n        return this.clerkjs.on(...args);\n      } else {\n        (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _eventBus).on(...args);\n      }\n    };\n    this.off = (...args) => {\n      var _a;\n      if ((_a = this.clerkjs) == null ? void 0 : _a.off) {\n        return this.clerkjs.off(...args);\n      } else {\n        (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _eventBus).off(...args);\n      }\n    };\n    /**\n     * @deprecated Please use `addStatusListener`. This api will be removed in the next major.\n     */\n    this.addOnLoaded = (cb) => {\n      this.loadedListeners.push(cb);\n      if (this.loaded) {\n        this.emitLoaded();\n      }\n    };\n    /**\n     * @deprecated Please use `__internal_setStatus`. This api will be removed in the next major.\n     */\n    this.emitLoaded = () => {\n      this.loadedListeners.forEach((cb) => cb());\n      this.loadedListeners = [];\n    };\n    this.beforeLoad = (clerkjs) => {\n      if (!clerkjs) {\n        throw new Error(\"Failed to hydrate latest Clerk JS\");\n      }\n    };\n    this.hydrateClerkJS = (clerkjs) => {\n      var _a;\n      if (!clerkjs) {\n        throw new Error(\"Failed to hydrate latest Clerk JS\");\n      }\n      this.clerkjs = clerkjs;\n      this.premountMethodCalls.forEach((cb) => cb());\n      this.premountAddListenerCalls.forEach((listenerHandlers, listener) => {\n        listenerHandlers.nativeUnsubscribe = clerkjs.addListener(listener);\n      });\n      (_a = (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _eventBus).internal.retrieveListeners(\"status\")) == null ? void 0 : _a.forEach((listener) => {\n        this.on(\"status\", listener, { notify: true });\n      });\n      if (this.preopenSignIn !== null) {\n        clerkjs.openSignIn(this.preopenSignIn);\n      }\n      if (this.preopenCheckout !== null) {\n        clerkjs.__internal_openCheckout(this.preopenCheckout);\n      }\n      if (this.preopenPlanDetails !== null) {\n        clerkjs.__internal_openPlanDetails(this.preopenPlanDetails);\n      }\n      if (this.preopenSubscriptionDetails !== null) {\n        clerkjs.__internal_openSubscriptionDetails(this.preopenSubscriptionDetails);\n      }\n      if (this.preopenSignUp !== null) {\n        clerkjs.openSignUp(this.preopenSignUp);\n      }\n      if (this.preopenUserProfile !== null) {\n        clerkjs.openUserProfile(this.preopenUserProfile);\n      }\n      if (this.preopenUserVerification !== null) {\n        clerkjs.__internal_openReverification(this.preopenUserVerification);\n      }\n      if (this.preopenOneTap !== null) {\n        clerkjs.openGoogleOneTap(this.preopenOneTap);\n      }\n      if (this.preopenOrganizationProfile !== null) {\n        clerkjs.openOrganizationProfile(this.preopenOrganizationProfile);\n      }\n      if (this.preopenCreateOrganization !== null) {\n        clerkjs.openCreateOrganization(this.preopenCreateOrganization);\n      }\n      if (this.preOpenWaitlist !== null) {\n        clerkjs.openWaitlist(this.preOpenWaitlist);\n      }\n      this.premountSignInNodes.forEach((props, node) => {\n        clerkjs.mountSignIn(node, props);\n      });\n      this.premountSignUpNodes.forEach((props, node) => {\n        clerkjs.mountSignUp(node, props);\n      });\n      this.premountUserProfileNodes.forEach((props, node) => {\n        clerkjs.mountUserProfile(node, props);\n      });\n      this.premountUserButtonNodes.forEach((props, node) => {\n        clerkjs.mountUserButton(node, props);\n      });\n      this.premountOrganizationListNodes.forEach((props, node) => {\n        clerkjs.mountOrganizationList(node, props);\n      });\n      this.premountWaitlistNodes.forEach((props, node) => {\n        clerkjs.mountWaitlist(node, props);\n      });\n      this.premountPricingTableNodes.forEach((props, node) => {\n        clerkjs.mountPricingTable(node, props);\n      });\n      this.premountApiKeysNodes.forEach((props, node) => {\n        clerkjs.mountApiKeys(node, props);\n      });\n      this.premountOAuthConsentNodes.forEach((props, node) => {\n        clerkjs.__internal_mountOAuthConsent(node, props);\n      });\n      this.premountTaskChooseOrganizationNodes.forEach((props, node) => {\n        clerkjs.mountTaskChooseOrganization(node, props);\n      });\n      if (typeof this.clerkjs.status === \"undefined\") {\n        (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _eventBus).emit(_clerk_shared_clerkEventBus__WEBPACK_IMPORTED_MODULE_10__.clerkEvents.Status, \"ready\");\n      }\n      this.emitLoaded();\n      return this.clerkjs;\n    };\n    this.__experimental_checkout = (...args) => {\n      var _a;\n      return (_a = this.clerkjs) == null ? void 0 : _a.__experimental_checkout(...args);\n    };\n    this.__unstable__updateProps = async (props) => {\n      const clerkjs = await (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateMethod)(this, _IsomorphicClerk_instances, waitForClerkJS_fn).call(this);\n      if (clerkjs && \"__unstable__updateProps\" in clerkjs) {\n        return clerkjs.__unstable__updateProps(props);\n      }\n    };\n    this.__internal_navigateToTaskIfAvailable = async (params) => {\n      if (this.clerkjs) {\n        return this.clerkjs.__internal_navigateToTaskIfAvailable(params);\n      } else {\n        return Promise.reject();\n      }\n    };\n    /**\n     * `setActive` can be used to set the active session and/or organization.\n     */\n    this.setActive = (params) => {\n      if (this.clerkjs) {\n        return this.clerkjs.setActive(params);\n      } else {\n        return Promise.reject();\n      }\n    };\n    this.openSignIn = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.openSignIn(props);\n      } else {\n        this.preopenSignIn = props;\n      }\n    };\n    this.closeSignIn = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.closeSignIn();\n      } else {\n        this.preopenSignIn = null;\n      }\n    };\n    this.__internal_openCheckout = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.__internal_openCheckout(props);\n      } else {\n        this.preopenCheckout = props;\n      }\n    };\n    this.__internal_closeCheckout = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.__internal_closeCheckout();\n      } else {\n        this.preopenCheckout = null;\n      }\n    };\n    this.__internal_openPlanDetails = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.__internal_openPlanDetails(props);\n      } else {\n        this.preopenPlanDetails = props;\n      }\n    };\n    this.__internal_closePlanDetails = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.__internal_closePlanDetails();\n      } else {\n        this.preopenPlanDetails = null;\n      }\n    };\n    this.__internal_openSubscriptionDetails = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.__internal_openSubscriptionDetails(props);\n      } else {\n        this.preopenSubscriptionDetails = props != null ? props : null;\n      }\n    };\n    this.__internal_closeSubscriptionDetails = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.__internal_closeSubscriptionDetails();\n      } else {\n        this.preopenSubscriptionDetails = null;\n      }\n    };\n    this.__internal_openReverification = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.__internal_openReverification(props);\n      } else {\n        this.preopenUserVerification = props;\n      }\n    };\n    this.__internal_closeReverification = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.__internal_closeReverification();\n      } else {\n        this.preopenUserVerification = null;\n      }\n    };\n    this.openGoogleOneTap = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.openGoogleOneTap(props);\n      } else {\n        this.preopenOneTap = props;\n      }\n    };\n    this.closeGoogleOneTap = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.closeGoogleOneTap();\n      } else {\n        this.preopenOneTap = null;\n      }\n    };\n    this.openUserProfile = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.openUserProfile(props);\n      } else {\n        this.preopenUserProfile = props;\n      }\n    };\n    this.closeUserProfile = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.closeUserProfile();\n      } else {\n        this.preopenUserProfile = null;\n      }\n    };\n    this.openOrganizationProfile = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.openOrganizationProfile(props);\n      } else {\n        this.preopenOrganizationProfile = props;\n      }\n    };\n    this.closeOrganizationProfile = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.closeOrganizationProfile();\n      } else {\n        this.preopenOrganizationProfile = null;\n      }\n    };\n    this.openCreateOrganization = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.openCreateOrganization(props);\n      } else {\n        this.preopenCreateOrganization = props;\n      }\n    };\n    this.closeCreateOrganization = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.closeCreateOrganization();\n      } else {\n        this.preopenCreateOrganization = null;\n      }\n    };\n    this.openWaitlist = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.openWaitlist(props);\n      } else {\n        this.preOpenWaitlist = props;\n      }\n    };\n    this.closeWaitlist = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.closeWaitlist();\n      } else {\n        this.preOpenWaitlist = null;\n      }\n    };\n    this.openSignUp = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.openSignUp(props);\n      } else {\n        this.preopenSignUp = props;\n      }\n    };\n    this.closeSignUp = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.closeSignUp();\n      } else {\n        this.preopenSignUp = null;\n      }\n    };\n    this.mountSignIn = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountSignIn(node, props);\n      } else {\n        this.premountSignInNodes.set(node, props);\n      }\n    };\n    this.unmountSignIn = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountSignIn(node);\n      } else {\n        this.premountSignInNodes.delete(node);\n      }\n    };\n    this.mountSignUp = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountSignUp(node, props);\n      } else {\n        this.premountSignUpNodes.set(node, props);\n      }\n    };\n    this.unmountSignUp = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountSignUp(node);\n      } else {\n        this.premountSignUpNodes.delete(node);\n      }\n    };\n    this.mountUserProfile = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountUserProfile(node, props);\n      } else {\n        this.premountUserProfileNodes.set(node, props);\n      }\n    };\n    this.unmountUserProfile = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountUserProfile(node);\n      } else {\n        this.premountUserProfileNodes.delete(node);\n      }\n    };\n    this.mountOrganizationProfile = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountOrganizationProfile(node, props);\n      } else {\n        this.premountOrganizationProfileNodes.set(node, props);\n      }\n    };\n    this.unmountOrganizationProfile = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountOrganizationProfile(node);\n      } else {\n        this.premountOrganizationProfileNodes.delete(node);\n      }\n    };\n    this.mountCreateOrganization = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountCreateOrganization(node, props);\n      } else {\n        this.premountCreateOrganizationNodes.set(node, props);\n      }\n    };\n    this.unmountCreateOrganization = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountCreateOrganization(node);\n      } else {\n        this.premountCreateOrganizationNodes.delete(node);\n      }\n    };\n    this.mountOrganizationSwitcher = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountOrganizationSwitcher(node, props);\n      } else {\n        this.premountOrganizationSwitcherNodes.set(node, props);\n      }\n    };\n    this.unmountOrganizationSwitcher = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountOrganizationSwitcher(node);\n      } else {\n        this.premountOrganizationSwitcherNodes.delete(node);\n      }\n    };\n    this.__experimental_prefetchOrganizationSwitcher = () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.__experimental_prefetchOrganizationSwitcher();\n      };\n      if (this.clerkjs && this.loaded) {\n        void callback();\n      } else {\n        this.premountMethodCalls.set(\"__experimental_prefetchOrganizationSwitcher\", callback);\n      }\n    };\n    this.mountOrganizationList = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountOrganizationList(node, props);\n      } else {\n        this.premountOrganizationListNodes.set(node, props);\n      }\n    };\n    this.unmountOrganizationList = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountOrganizationList(node);\n      } else {\n        this.premountOrganizationListNodes.delete(node);\n      }\n    };\n    this.mountUserButton = (node, userButtonProps) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountUserButton(node, userButtonProps);\n      } else {\n        this.premountUserButtonNodes.set(node, userButtonProps);\n      }\n    };\n    this.unmountUserButton = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountUserButton(node);\n      } else {\n        this.premountUserButtonNodes.delete(node);\n      }\n    };\n    this.mountWaitlist = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountWaitlist(node, props);\n      } else {\n        this.premountWaitlistNodes.set(node, props);\n      }\n    };\n    this.unmountWaitlist = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountWaitlist(node);\n      } else {\n        this.premountWaitlistNodes.delete(node);\n      }\n    };\n    this.mountPricingTable = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountPricingTable(node, props);\n      } else {\n        this.premountPricingTableNodes.set(node, props);\n      }\n    };\n    this.unmountPricingTable = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountPricingTable(node);\n      } else {\n        this.premountPricingTableNodes.delete(node);\n      }\n    };\n    this.mountApiKeys = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountApiKeys(node, props);\n      } else {\n        this.premountApiKeysNodes.set(node, props);\n      }\n    };\n    this.unmountApiKeys = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountApiKeys(node);\n      } else {\n        this.premountApiKeysNodes.delete(node);\n      }\n    };\n    this.__internal_mountOAuthConsent = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.__internal_mountOAuthConsent(node, props);\n      } else {\n        this.premountOAuthConsentNodes.set(node, props);\n      }\n    };\n    this.__internal_unmountOAuthConsent = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.__internal_unmountOAuthConsent(node);\n      } else {\n        this.premountOAuthConsentNodes.delete(node);\n      }\n    };\n    this.mountTaskChooseOrganization = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountTaskChooseOrganization(node, props);\n      } else {\n        this.premountTaskChooseOrganizationNodes.set(node, props);\n      }\n    };\n    this.unmountTaskChooseOrganization = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountTaskChooseOrganization(node);\n      } else {\n        this.premountTaskChooseOrganizationNodes.delete(node);\n      }\n    };\n    this.addListener = (listener) => {\n      if (this.clerkjs) {\n        return this.clerkjs.addListener(listener);\n      } else {\n        const unsubscribe = () => {\n          var _a;\n          const listenerHandlers = this.premountAddListenerCalls.get(listener);\n          if (listenerHandlers) {\n            (_a = listenerHandlers.nativeUnsubscribe) == null ? void 0 : _a.call(listenerHandlers);\n            this.premountAddListenerCalls.delete(listener);\n          }\n        };\n        this.premountAddListenerCalls.set(listener, { unsubscribe, nativeUnsubscribe: void 0 });\n        return unsubscribe;\n      }\n    };\n    this.navigate = (to) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.navigate(to);\n      };\n      if (this.clerkjs && this.loaded) {\n        void callback();\n      } else {\n        this.premountMethodCalls.set(\"navigate\", callback);\n      }\n    };\n    this.redirectWithAuth = async (...args) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectWithAuth(...args);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectWithAuth\", callback);\n        return;\n      }\n    };\n    this.redirectToSignIn = async (opts) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToSignIn(opts);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToSignIn\", callback);\n        return;\n      }\n    };\n    this.redirectToSignUp = async (opts) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToSignUp(opts);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToSignUp\", callback);\n        return;\n      }\n    };\n    this.redirectToUserProfile = async () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToUserProfile();\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToUserProfile\", callback);\n        return;\n      }\n    };\n    this.redirectToAfterSignUp = () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToAfterSignUp();\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToAfterSignUp\", callback);\n      }\n    };\n    this.redirectToAfterSignIn = () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToAfterSignIn();\n      };\n      if (this.clerkjs && this.loaded) {\n        callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToAfterSignIn\", callback);\n      }\n    };\n    this.redirectToAfterSignOut = () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToAfterSignOut();\n      };\n      if (this.clerkjs && this.loaded) {\n        callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToAfterSignOut\", callback);\n      }\n    };\n    this.redirectToOrganizationProfile = async () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToOrganizationProfile();\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToOrganizationProfile\", callback);\n        return;\n      }\n    };\n    this.redirectToCreateOrganization = async () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToCreateOrganization();\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToCreateOrganization\", callback);\n        return;\n      }\n    };\n    this.redirectToWaitlist = async () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToWaitlist();\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToWaitlist\", callback);\n        return;\n      }\n    };\n    this.handleRedirectCallback = async (params) => {\n      var _a;\n      const callback = () => {\n        var _a2;\n        return (_a2 = this.clerkjs) == null ? void 0 : _a2.handleRedirectCallback(params);\n      };\n      if (this.clerkjs && this.loaded) {\n        void ((_a = callback()) == null ? void 0 : _a.catch(() => {\n        }));\n      } else {\n        this.premountMethodCalls.set(\"handleRedirectCallback\", callback);\n      }\n    };\n    this.handleGoogleOneTapCallback = async (signInOrUp, params) => {\n      var _a;\n      const callback = () => {\n        var _a2;\n        return (_a2 = this.clerkjs) == null ? void 0 : _a2.handleGoogleOneTapCallback(signInOrUp, params);\n      };\n      if (this.clerkjs && this.loaded) {\n        void ((_a = callback()) == null ? void 0 : _a.catch(() => {\n        }));\n      } else {\n        this.premountMethodCalls.set(\"handleGoogleOneTapCallback\", callback);\n      }\n    };\n    this.handleEmailLinkVerification = async (params) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.handleEmailLinkVerification(params);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"handleEmailLinkVerification\", callback);\n      }\n    };\n    this.authenticateWithMetamask = async (params) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.authenticateWithMetamask(params);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"authenticateWithMetamask\", callback);\n      }\n    };\n    this.authenticateWithCoinbaseWallet = async (params) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.authenticateWithCoinbaseWallet(params);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"authenticateWithCoinbaseWallet\", callback);\n      }\n    };\n    this.authenticateWithOKXWallet = async (params) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.authenticateWithOKXWallet(params);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"authenticateWithOKXWallet\", callback);\n      }\n    };\n    this.authenticateWithWeb3 = async (params) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.authenticateWithWeb3(params);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"authenticateWithWeb3\", callback);\n      }\n    };\n    this.authenticateWithGoogleOneTap = async (params) => {\n      const clerkjs = await (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateMethod)(this, _IsomorphicClerk_instances, waitForClerkJS_fn).call(this);\n      return clerkjs.authenticateWithGoogleOneTap(params);\n    };\n    this.__internal_loadStripeJs = async () => {\n      const clerkjs = await (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateMethod)(this, _IsomorphicClerk_instances, waitForClerkJS_fn).call(this);\n      return clerkjs.__internal_loadStripeJs();\n    };\n    this.createOrganization = async (params) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.createOrganization(params);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"createOrganization\", callback);\n      }\n    };\n    this.getOrganization = async (organizationId) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.getOrganization(organizationId);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"getOrganization\", callback);\n      }\n    };\n    this.joinWaitlist = async (params) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.joinWaitlist(params);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"joinWaitlist\", callback);\n      }\n    };\n    this.signOut = async (...args) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.signOut(...args);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"signOut\", callback);\n      }\n    };\n    const { Clerk = null, publishableKey } = options || {};\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateSet)(this, _publishableKey, publishableKey);\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateSet)(this, _proxyUrl, options == null ? void 0 : options.proxyUrl);\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateSet)(this, _domain, options == null ? void 0 : options.domain);\n    this.options = options;\n    this.Clerk = Clerk;\n    this.mode = (0,_clerk_shared_browser__WEBPACK_IMPORTED_MODULE_9__.inBrowser)() ? \"browser\" : \"server\";\n    if (!this.options.sdkMetadata) {\n      this.options.sdkMetadata = SDK_METADATA;\n    }\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _eventBus).emit(_clerk_shared_clerkEventBus__WEBPACK_IMPORTED_MODULE_10__.clerkEvents.Status, \"loading\");\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _eventBus).prioritizedOn(_clerk_shared_clerkEventBus__WEBPACK_IMPORTED_MODULE_10__.clerkEvents.Status, (status) => (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateSet)(this, _status, status));\n    if ((0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _publishableKey)) {\n      void this.loadClerkJS();\n    }\n  }\n  get publishableKey() {\n    return (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _publishableKey);\n  }\n  get loaded() {\n    var _a;\n    return ((_a = this.clerkjs) == null ? void 0 : _a.loaded) || false;\n  }\n  get status() {\n    var _a;\n    if (!this.clerkjs) {\n      return (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _status);\n    }\n    return ((_a = this.clerkjs) == null ? void 0 : _a.status) || /**\n     * Support older clerk-js versions.\n     * If clerk-js is available but `.status` is missing it we need to fallback to `.loaded`.\n     * Since \"degraded\" an \"error\" did not exist before,\n     * map \"loaded\" to \"ready\" and \"not loaded\" to \"loading\".\n     */\n    (this.clerkjs.loaded ? \"ready\" : \"loading\");\n  }\n  static getOrCreateInstance(options) {\n    if (!(0,_clerk_shared_browser__WEBPACK_IMPORTED_MODULE_9__.inBrowser)() || !(0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _instance) || options.Clerk && (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _instance).Clerk !== options.Clerk || // Allow hot swapping PKs on the client\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _instance).publishableKey !== options.publishableKey) {\n      (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateSet)(this, _instance, new _IsomorphicClerk(options));\n    }\n    return (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _instance);\n  }\n  static clearInstance() {\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateSet)(this, _instance, null);\n  }\n  get domain() {\n    if (typeof window !== \"undefined\" && window.location) {\n      return (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_11__.handleValueOrFn)((0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _domain), new URL(window.location.href), \"\");\n    }\n    if (typeof (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _domain) === \"function\") {\n      return _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.errorThrower.throw(_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _domain) || \"\";\n  }\n  get proxyUrl() {\n    if (typeof window !== \"undefined\" && window.location) {\n      return (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_11__.handleValueOrFn)((0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _proxyUrl), new URL(window.location.href), \"\");\n    }\n    if (typeof (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _proxyUrl) === \"function\") {\n      return _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.errorThrower.throw(_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _proxyUrl) || \"\";\n  }\n  /**\n   * Accesses private options from the `Clerk` instance and defaults to\n   * `IsomorphicClerk` options when in SSR context.\n   *  @internal\n   */\n  __internal_getOption(key) {\n    var _a, _b;\n    return ((_a = this.clerkjs) == null ? void 0 : _a.__internal_getOption) ? (_b = this.clerkjs) == null ? void 0 : _b.__internal_getOption(key) : this.options[key];\n  }\n  get sdkMetadata() {\n    var _a;\n    return ((_a = this.clerkjs) == null ? void 0 : _a.sdkMetadata) || this.options.sdkMetadata || void 0;\n  }\n  get instanceType() {\n    var _a;\n    return (_a = this.clerkjs) == null ? void 0 : _a.instanceType;\n  }\n  get frontendApi() {\n    var _a;\n    return ((_a = this.clerkjs) == null ? void 0 : _a.frontendApi) || \"\";\n  }\n  get isStandardBrowser() {\n    var _a;\n    return ((_a = this.clerkjs) == null ? void 0 : _a.isStandardBrowser) || this.options.standardBrowser || false;\n  }\n  get isSatellite() {\n    if (typeof window !== \"undefined\" && window.location) {\n      return (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_11__.handleValueOrFn)(this.options.isSatellite, new URL(window.location.href), false);\n    }\n    if (typeof this.options.isSatellite === \"function\") {\n      return _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.errorThrower.throw(_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return false;\n  }\n  async loadClerkJS() {\n    var _a;\n    if (this.mode !== \"browser\" || this.loaded) {\n      return;\n    }\n    if (typeof window !== \"undefined\") {\n      window.__clerk_publishable_key = (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _publishableKey);\n      window.__clerk_proxy_url = this.proxyUrl;\n      window.__clerk_domain = this.domain;\n    }\n    try {\n      if (this.Clerk) {\n        let c;\n        if ((0,_chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.isConstructor)(this.Clerk)) {\n          c = new this.Clerk((0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _publishableKey), {\n            proxyUrl: this.proxyUrl,\n            domain: this.domain\n          });\n          this.beforeLoad(c);\n          await c.load(this.options);\n        } else {\n          c = this.Clerk;\n          if (!c.loaded) {\n            this.beforeLoad(c);\n            await c.load(this.options);\n          }\n        }\n        global.Clerk = c;\n      } else if (!__BUILD_DISABLE_RHC__) {\n        if (!global.Clerk) {\n          await (0,_clerk_shared_loadClerkJsScript__WEBPACK_IMPORTED_MODULE_4__.loadClerkJsScript)({\n            ...this.options,\n            publishableKey: (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _publishableKey),\n            proxyUrl: this.proxyUrl,\n            domain: this.domain,\n            nonce: this.options.nonce\n          });\n        }\n        if (!global.Clerk) {\n          throw new Error(\"Failed to download latest ClerkJS. Contact <EMAIL>.\");\n        }\n        this.beforeLoad(global.Clerk);\n        await global.Clerk.load(this.options);\n      }\n      if ((_a = global.Clerk) == null ? void 0 : _a.loaded) {\n        return this.hydrateClerkJS(global.Clerk);\n      }\n      return;\n    } catch (err) {\n      const error = err;\n      (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateGet)(this, _eventBus).emit(_clerk_shared_clerkEventBus__WEBPACK_IMPORTED_MODULE_10__.clerkEvents.Status, \"error\");\n      console.error(error.stack || error.message || error);\n      return;\n    }\n  }\n  get version() {\n    var _a;\n    return (_a = this.clerkjs) == null ? void 0 : _a.version;\n  }\n  get client() {\n    if (this.clerkjs) {\n      return this.clerkjs.client;\n    } else {\n      return void 0;\n    }\n  }\n  get session() {\n    if (this.clerkjs) {\n      return this.clerkjs.session;\n    } else {\n      return void 0;\n    }\n  }\n  get user() {\n    if (this.clerkjs) {\n      return this.clerkjs.user;\n    } else {\n      return void 0;\n    }\n  }\n  get organization() {\n    if (this.clerkjs) {\n      return this.clerkjs.organization;\n    } else {\n      return void 0;\n    }\n  }\n  get telemetry() {\n    if (this.clerkjs) {\n      return this.clerkjs.telemetry;\n    } else {\n      return void 0;\n    }\n  }\n  get __unstable__environment() {\n    if (this.clerkjs) {\n      return this.clerkjs.__unstable__environment;\n    } else {\n      return void 0;\n    }\n  }\n  get isSignedIn() {\n    if (this.clerkjs) {\n      return this.clerkjs.isSignedIn;\n    } else {\n      return false;\n    }\n  }\n  get billing() {\n    var _a;\n    return (_a = this.clerkjs) == null ? void 0 : _a.billing;\n  }\n  get __internal_state() {\n    var _a;\n    return (_a = this.clerkjs) == null ? void 0 : _a.__internal_state;\n  }\n  get apiKeys() {\n    var _a;\n    return (_a = this.clerkjs) == null ? void 0 : _a.apiKeys;\n  }\n  __unstable__setEnvironment(...args) {\n    if (this.clerkjs && \"__unstable__setEnvironment\" in this.clerkjs) {\n      this.clerkjs.__unstable__setEnvironment(args);\n    } else {\n      return void 0;\n    }\n  }\n};\n_status = new WeakMap();\n_domain = new WeakMap();\n_proxyUrl = new WeakMap();\n_publishableKey = new WeakMap();\n_eventBus = new WeakMap();\n_instance = new WeakMap();\n_IsomorphicClerk_instances = new WeakSet();\nwaitForClerkJS_fn = function() {\n  return new Promise((resolve) => {\n    this.addOnLoaded(() => resolve(this.clerkjs));\n  });\n};\n(0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_3__.__privateAdd)(_IsomorphicClerk, _instance);\nvar IsomorphicClerk = _IsomorphicClerk;\n\n// src/contexts/ClerkContextProvider.tsx\nfunction ClerkContextProvider(props) {\n  const { isomorphicClerkOptions, initialState, children } = props;\n  const { isomorphicClerk: clerk, clerkStatus } = useLoadedIsomorphicClerk(isomorphicClerkOptions);\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_5__.useState({\n    client: clerk.client,\n    session: clerk.session,\n    user: clerk.user,\n    organization: clerk.organization\n  });\n  react__WEBPACK_IMPORTED_MODULE_5__.useEffect(() => {\n    return clerk.addListener((e) => setState({ ...e }));\n  }, []);\n  const derivedState = (0,_clerk_shared_deriveState__WEBPACK_IMPORTED_MODULE_7__.deriveState)(clerk.loaded, state, initialState);\n  const clerkCtx = react__WEBPACK_IMPORTED_MODULE_5__.useMemo(\n    () => ({ value: clerk }),\n    [\n      // Only update the clerk reference on status change\n      clerkStatus\n    ]\n  );\n  const clientCtx = react__WEBPACK_IMPORTED_MODULE_5__.useMemo(() => ({ value: state.client }), [state.client]);\n  const {\n    sessionId,\n    sessionStatus,\n    sessionClaims,\n    session,\n    userId,\n    user,\n    orgId,\n    actor,\n    organization,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n    factorVerificationAge\n  } = derivedState;\n  const authCtx = react__WEBPACK_IMPORTED_MODULE_5__.useMemo(() => {\n    const value = {\n      sessionId,\n      sessionStatus,\n      sessionClaims,\n      userId,\n      actor,\n      orgId,\n      orgRole,\n      orgSlug,\n      orgPermissions,\n      factorVerificationAge\n    };\n    return { value };\n  }, [sessionId, sessionStatus, userId, actor, orgId, orgRole, orgSlug, factorVerificationAge, sessionClaims == null ? void 0 : sessionClaims.__raw]);\n  const sessionCtx = react__WEBPACK_IMPORTED_MODULE_5__.useMemo(() => ({ value: session }), [sessionId, session]);\n  const userCtx = react__WEBPACK_IMPORTED_MODULE_5__.useMemo(() => ({ value: user }), [userId, user]);\n  const organizationCtx = react__WEBPACK_IMPORTED_MODULE_5__.useMemo(() => {\n    const value = {\n      organization\n    };\n    return { value };\n  }, [orgId, organization]);\n  return (\n    // @ts-expect-error value passed is of type IsomorphicClerk where the context expects LoadedClerk\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_5__.createElement(_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.IsomorphicClerkContext.Provider, { value: clerkCtx }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_5__.createElement(_clerk_shared_react__WEBPACK_IMPORTED_MODULE_8__.ClientContext.Provider, { value: clientCtx }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_5__.createElement(_clerk_shared_react__WEBPACK_IMPORTED_MODULE_8__.SessionContext.Provider, { value: sessionCtx }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_5__.createElement(_clerk_shared_react__WEBPACK_IMPORTED_MODULE_8__.OrganizationProvider, { ...organizationCtx.value }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_5__.createElement(_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.AuthContext.Provider, { value: authCtx }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_5__.createElement(_clerk_shared_react__WEBPACK_IMPORTED_MODULE_8__.UserContext.Provider, { value: userCtx }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_5__.createElement(\n      _clerk_shared_react__WEBPACK_IMPORTED_MODULE_8__.__experimental_CheckoutProvider,\n      {\n        value: void 0\n      },\n      children\n    )))))))\n  );\n}\nvar useLoadedIsomorphicClerk = (options) => {\n  const isomorphicClerkRef = react__WEBPACK_IMPORTED_MODULE_5__.useRef(IsomorphicClerk.getOrCreateInstance(options));\n  const [clerkStatus, setClerkStatus] = react__WEBPACK_IMPORTED_MODULE_5__.useState(isomorphicClerkRef.current.status);\n  react__WEBPACK_IMPORTED_MODULE_5__.useEffect(() => {\n    void isomorphicClerkRef.current.__unstable__updateProps({ appearance: options.appearance });\n  }, [options.appearance]);\n  react__WEBPACK_IMPORTED_MODULE_5__.useEffect(() => {\n    void isomorphicClerkRef.current.__unstable__updateProps({ options });\n  }, [options.localization]);\n  react__WEBPACK_IMPORTED_MODULE_5__.useEffect(() => {\n    isomorphicClerkRef.current.on(\"status\", setClerkStatus);\n    return () => {\n      if (isomorphicClerkRef.current) {\n        isomorphicClerkRef.current.off(\"status\", setClerkStatus);\n      }\n      IsomorphicClerk.clearInstance();\n    };\n  }, []);\n  return { isomorphicClerk: isomorphicClerkRef.current, clerkStatus };\n};\n\n// src/contexts/ClerkProvider.tsx\nfunction ClerkProviderBase(props) {\n  const { initialState, children, __internal_bypassMissingPublishableKey, ...restIsomorphicClerkOptions } = props;\n  const { publishableKey = \"\", Clerk: userInitialisedClerk } = restIsomorphicClerkOptions;\n  if (!userInitialisedClerk && !__internal_bypassMissingPublishableKey) {\n    if (!publishableKey) {\n      _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.errorThrower.throwMissingPublishableKeyError();\n    } else if (publishableKey && !(0,_clerk_shared_keys__WEBPACK_IMPORTED_MODULE_6__.isPublishableKey)(publishableKey)) {\n      _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.errorThrower.throwInvalidPublishableKeyError({ key: publishableKey });\n    }\n  }\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_5__.createElement(\n    ClerkContextProvider,\n    {\n      initialState,\n      isomorphicClerkOptions: restIsomorphicClerkOptions\n    },\n    children\n  );\n}\nvar ClerkProvider = (0,_chunk_3664V5SS_mjs__WEBPACK_IMPORTED_MODULE_1__.withMaxAllowedInstancesGuard)(ClerkProviderBase, \"ClerkProvider\", _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.multipleClerkProvidersError);\nClerkProvider.displayName = \"ClerkProvider\";\n\n// src/index.ts\n(0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_2__.setErrorThrowerOptions)({ packageName: \"@clerk/clerk-react\" });\n(0,_clerk_shared_loadClerkJsScript__WEBPACK_IMPORTED_MODULE_4__.setClerkJsLoadingErrorPackageName)(\"@clerk/clerk-react\");\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/internal.mjs":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/internal.mjs ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MultisessionAppSupport: () => (/* reexport safe */ _chunk_WXC6AKVT_mjs__WEBPACK_IMPORTED_MODULE_0__.MultisessionAppSupport),\n/* harmony export */   buildClerkJsScriptAttributes: () => (/* reexport safe */ _clerk_shared_loadClerkJsScript__WEBPACK_IMPORTED_MODULE_3__.buildClerkJsScriptAttributes),\n/* harmony export */   clerkJsScriptUrl: () => (/* reexport safe */ _clerk_shared_loadClerkJsScript__WEBPACK_IMPORTED_MODULE_3__.clerkJsScriptUrl),\n/* harmony export */   setClerkJsLoadingErrorPackageName: () => (/* reexport safe */ _clerk_shared_loadClerkJsScript__WEBPACK_IMPORTED_MODULE_3__.setClerkJsLoadingErrorPackageName),\n/* harmony export */   setErrorThrowerOptions: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_1__.setErrorThrowerOptions),\n/* harmony export */   useDerivedAuth: () => (/* reexport safe */ _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_1__.useDerivedAuth),\n/* harmony export */   useRoutingProps: () => (/* binding */ useRoutingProps)\n/* harmony export */ });\n/* harmony import */ var _chunk_WXC6AKVT_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-WXC6AKVT.mjs */ \"(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-WXC6AKVT.mjs\");\n/* harmony import */ var _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-KVSNHZPC.mjs */ \"(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-KVSNHZPC.mjs\");\n/* harmony import */ var _chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-OANWQR3B.mjs */ \"(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/chunk-OANWQR3B.mjs\");\n/* harmony import */ var _clerk_shared_loadClerkJsScript__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @clerk/shared/loadClerkJsScript */ \"(ssr)/./node_modules/.pnpm/@clerk+shared@3.19.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/shared/dist/loadClerkJsScript.mjs\");\n\n\n\n\n// src/hooks/useRoutingProps.ts\nfunction useRoutingProps(componentName, props, routingOptions) {\n  const path = props.path || (routingOptions == null ? void 0 : routingOptions.path);\n  const routing = props.routing || (routingOptions == null ? void 0 : routingOptions.routing) || \"path\";\n  if (routing === \"path\") {\n    if (!path) {\n      return _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_1__.errorThrower.throw((0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_1__.noPathProvidedError)(componentName));\n    }\n    return {\n      ...routingOptions,\n      ...props,\n      routing: \"path\"\n    };\n  }\n  if (props.path) {\n    return _chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_1__.errorThrower.throw((0,_chunk_KVSNHZPC_mjs__WEBPACK_IMPORTED_MODULE_1__.incompatibleRoutingWithPathProvidedError)(componentName));\n  }\n  return {\n    ...routingOptions,\n    ...props,\n    path: void 0\n  };\n}\n\n// src/internal.ts\n\n\n//# sourceMappingURL=internal.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@clerk+clerk-react@5.41.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@clerk/clerk-react/dist/internal.mjs\n");

/***/ })

};
;