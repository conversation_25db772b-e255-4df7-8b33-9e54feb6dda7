"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/swr@2.3.4_react@19.1.1";
exports.ids = ["vendor-chunks/swr@2.3.4_react@19.1.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (/* binding */ noop),\n/* harmony export */   B: () => (/* binding */ isPromiseLike),\n/* harmony export */   I: () => (/* binding */ IS_REACT_LEGACY),\n/* harmony export */   O: () => (/* binding */ OBJECT),\n/* harmony export */   S: () => (/* binding */ SWRConfigContext),\n/* harmony export */   U: () => (/* binding */ UNDEFINED),\n/* harmony export */   a: () => (/* binding */ isFunction),\n/* harmony export */   b: () => (/* binding */ SWRGlobalState),\n/* harmony export */   c: () => (/* binding */ cache),\n/* harmony export */   d: () => (/* binding */ defaultConfig),\n/* harmony export */   e: () => (/* binding */ isUndefined),\n/* harmony export */   f: () => (/* binding */ mergeConfigs),\n/* harmony export */   g: () => (/* binding */ SWRConfig),\n/* harmony export */   h: () => (/* binding */ initCache),\n/* harmony export */   i: () => (/* binding */ isWindowDefined),\n/* harmony export */   j: () => (/* binding */ mutate),\n/* harmony export */   k: () => (/* binding */ compare),\n/* harmony export */   l: () => (/* binding */ stableHash),\n/* harmony export */   m: () => (/* binding */ mergeObjects),\n/* harmony export */   n: () => (/* binding */ internalMutate),\n/* harmony export */   o: () => (/* binding */ getTimestamp),\n/* harmony export */   p: () => (/* binding */ preset),\n/* harmony export */   q: () => (/* binding */ defaultConfigOptions),\n/* harmony export */   r: () => (/* binding */ IS_SERVER),\n/* harmony export */   s: () => (/* binding */ serialize),\n/* harmony export */   t: () => (/* binding */ rAF),\n/* harmony export */   u: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   v: () => (/* binding */ slowConnection),\n/* harmony export */   w: () => (/* binding */ isDocumentDefined),\n/* harmony export */   x: () => (/* binding */ isLegacyDeno),\n/* harmony export */   y: () => (/* binding */ hasRequestAnimationFrame),\n/* harmony export */   z: () => (/* binding */ createCacheHelper)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _events_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./events.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var dequal_lite__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dequal/lite */ \"(ssr)/./node_modules/.pnpm/dequal@2.0.3/node_modules/dequal/lite/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ A,B,I,O,S,U,a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z auto */ \n\n\n// Global state used to deduplicate requests and store listeners\nconst SWRGlobalState = new WeakMap();\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\nconst mergeObjects = (a, b)=>({\n        ...a,\n        ...b\n    });\nconst isPromiseLike = (x)=>isFunction(x.then);\nconst EMPTY_CACHE = {};\nconst INITIAL_CACHE = {};\nconst STR_UNDEFINED = 'undefined';\n// NOTE: Use the function to guarantee it's re-evaluated between jsdom and node runtime for tests.\nconst isWindowDefined = \"undefined\" != STR_UNDEFINED;\nconst isDocumentDefined = typeof document != STR_UNDEFINED;\nconst isLegacyDeno = isWindowDefined && 'Deno' in window;\nconst hasRequestAnimationFrame = ()=>isWindowDefined && typeof window['requestAnimationFrame'] != STR_UNDEFINED;\nconst createCacheHelper = (cache, key)=>{\n    const state = SWRGlobalState.get(cache);\n    return [\n        // Getter\n        ()=>!isUndefined(key) && cache.get(key) || EMPTY_CACHE,\n        // Setter\n        (info)=>{\n            if (!isUndefined(key)) {\n                const prev = cache.get(key);\n                // Before writing to the store, we keep the value in the initial cache\n                // if it's not there yet.\n                if (!(key in INITIAL_CACHE)) {\n                    INITIAL_CACHE[key] = prev;\n                }\n                state[5](key, mergeObjects(prev, info), prev || EMPTY_CACHE);\n            }\n        },\n        // Subscriber\n        state[6],\n        // Get server cache snapshot\n        ()=>{\n            if (!isUndefined(key)) {\n                // If the cache was updated on the client, we return the stored initial value.\n                if (key in INITIAL_CACHE) return INITIAL_CACHE[key];\n            }\n            // If we haven't done any client-side updates, we return the current value.\n            return !isUndefined(key) && cache.get(key) || EMPTY_CACHE;\n        }\n    ];\n} // export { UNDEFINED, OBJECT, isUndefined, isFunction, mergeObjects, isPromiseLike }\n;\n/**\n * Due to the bug https://bugs.chromium.org/p/chromium/issues/detail?id=678075,\n * it's not reliable to detect if the browser is currently online or offline\n * based on `navigator.onLine`.\n * As a workaround, we always assume it's online on the first load, and change\n * the status upon `online` or `offline` events.\n */ let online = true;\nconst isOnline = ()=>online;\n// For node and React Native, `add/removeEventListener` doesn't exist on window.\nconst [onWindowEvent, offWindowEvent] = isWindowDefined && window.addEventListener ? [\n    window.addEventListener.bind(window),\n    window.removeEventListener.bind(window)\n] : [\n    noop,\n    noop\n];\nconst isVisible = ()=>{\n    const visibilityState = isDocumentDefined && document.visibilityState;\n    return isUndefined(visibilityState) || visibilityState !== 'hidden';\n};\nconst initFocus = (callback)=>{\n    // focus revalidate\n    if (isDocumentDefined) {\n        document.addEventListener('visibilitychange', callback);\n    }\n    onWindowEvent('focus', callback);\n    return ()=>{\n        if (isDocumentDefined) {\n            document.removeEventListener('visibilitychange', callback);\n        }\n        offWindowEvent('focus', callback);\n    };\n};\nconst initReconnect = (callback)=>{\n    // revalidate on reconnected\n    const onOnline = ()=>{\n        online = true;\n        callback();\n    };\n    // nothing to revalidate, just update the status\n    const onOffline = ()=>{\n        online = false;\n    };\n    onWindowEvent('online', onOnline);\n    onWindowEvent('offline', onOffline);\n    return ()=>{\n        offWindowEvent('online', onOnline);\n        offWindowEvent('offline', onOffline);\n    };\n};\nconst preset = {\n    isOnline,\n    isVisible\n};\nconst defaultConfigOptions = {\n    initFocus,\n    initReconnect\n};\nconst IS_REACT_LEGACY = !react__WEBPACK_IMPORTED_MODULE_0__.useId;\nconst IS_SERVER = !isWindowDefined || isLegacyDeno;\n// Polyfill requestAnimationFrame\nconst rAF = (f)=>hasRequestAnimationFrame() ? window['requestAnimationFrame'](f) : setTimeout(f, 1);\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nconst useIsomorphicLayoutEffect = IS_SERVER ? react__WEBPACK_IMPORTED_MODULE_0__.useEffect : react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect;\n// This assignment is to extend the Navigator type to use effectiveType.\nconst navigatorConnection = typeof navigator !== 'undefined' && navigator.connection;\n// Adjust the config based on slow connection status (<= 70Kbps).\nconst slowConnection = !IS_SERVER && navigatorConnection && ([\n    'slow-2g',\n    '2g'\n].includes(navigatorConnection.effectiveType) || navigatorConnection.saveData);\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst getTypeName = (value)=>OBJECT.prototype.toString.call(value);\nconst isObjectTypeName = (typeName, type)=>typeName === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const typeName = getTypeName(arg);\n    const isDate = isObjectTypeName(typeName, 'Date');\n    const isRegex = isObjectTypeName(typeName, 'RegExp');\n    const isPlainObject = isObjectTypeName(typeName, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n// Global timestamp.\nlet __timestamp = 0;\nconst getTimestamp = ()=>++__timestamp;\nasync function internalMutate(...args) {\n    const [cache, _key, _data, _opts] = args;\n    // When passing as a boolean, it's explicitly used to disable/enable\n    // revalidation.\n    const options = mergeObjects({\n        populateCache: true,\n        throwOnError: true\n    }, typeof _opts === 'boolean' ? {\n        revalidate: _opts\n    } : _opts || {});\n    let populateCache = options.populateCache;\n    const rollbackOnErrorOption = options.rollbackOnError;\n    let optimisticData = options.optimisticData;\n    const rollbackOnError = (error)=>{\n        return typeof rollbackOnErrorOption === 'function' ? rollbackOnErrorOption(error) : rollbackOnErrorOption !== false;\n    };\n    const throwOnError = options.throwOnError;\n    // If the second argument is a key filter, return the mutation results for all\n    // filtered keys.\n    if (isFunction(_key)) {\n        const keyFilter = _key;\n        const matchedKeys = [];\n        const it = cache.keys();\n        for (const key of it){\n            if (!/^\\$(inf|sub)\\$/.test(key) && keyFilter(cache.get(key)._k)) {\n                matchedKeys.push(key);\n            }\n        }\n        return Promise.all(matchedKeys.map(mutateByKey));\n    }\n    return mutateByKey(_key);\n    async function mutateByKey(_k) {\n        // Serialize key\n        const [key] = serialize(_k);\n        if (!key) return;\n        const [get, set] = createCacheHelper(cache, key);\n        const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n        const startRevalidate = ()=>{\n            const revalidators = EVENT_REVALIDATORS[key];\n            const revalidate = isFunction(options.revalidate) ? options.revalidate(get().data, _k) : options.revalidate !== false;\n            if (revalidate) {\n                // Invalidate the key by deleting the concurrent request markers so new\n                // requests will not be deduped.\n                delete FETCH[key];\n                delete PRELOAD[key];\n                if (revalidators && revalidators[0]) {\n                    return revalidators[0](_events_mjs__WEBPACK_IMPORTED_MODULE_2__.MUTATE_EVENT).then(()=>get().data);\n                }\n            }\n            return get().data;\n        };\n        // If there is no new data provided, revalidate the key with current state.\n        if (args.length < 3) {\n            // Revalidate and broadcast state.\n            return startRevalidate();\n        }\n        let data = _data;\n        let error;\n        let isError = false;\n        // Update global timestamps.\n        const beforeMutationTs = getTimestamp();\n        MUTATION[key] = [\n            beforeMutationTs,\n            0\n        ];\n        const hasOptimisticData = !isUndefined(optimisticData);\n        const state = get();\n        // `displayedData` is the current value on screen. It could be the optimistic value\n        // that is going to be overridden by a `committedData`, or get reverted back.\n        // `committedData` is the validated value that comes from a fetch or mutation.\n        const displayedData = state.data;\n        const currentData = state._c;\n        const committedData = isUndefined(currentData) ? displayedData : currentData;\n        // Do optimistic data update.\n        if (hasOptimisticData) {\n            optimisticData = isFunction(optimisticData) ? optimisticData(committedData, displayedData) : optimisticData;\n            // When we set optimistic data, backup the current committedData data in `_c`.\n            set({\n                data: optimisticData,\n                _c: committedData\n            });\n        }\n        if (isFunction(data)) {\n            // `data` is a function, call it passing current cache value.\n            try {\n                data = data(committedData);\n            } catch (err) {\n                // If it throws an error synchronously, we shouldn't update the cache.\n                error = err;\n                isError = true;\n            }\n        }\n        // `data` is a promise/thenable, resolve the final data first.\n        if (data && isPromiseLike(data)) {\n            // This means that the mutation is async, we need to check timestamps to\n            // avoid race conditions.\n            data = await data.catch((err)=>{\n                error = err;\n                isError = true;\n            });\n            // Check if other mutations have occurred since we've started this mutation.\n            // If there's a race we don't update cache or broadcast the change,\n            // just return the data.\n            if (beforeMutationTs !== MUTATION[key][0]) {\n                if (isError) throw error;\n                return data;\n            } else if (isError && hasOptimisticData && rollbackOnError(error)) {\n                // Rollback. Always populate the cache in this case but without\n                // transforming the data.\n                populateCache = true;\n                // Reset data to be the latest committed data, and clear the `_c` value.\n                set({\n                    data: committedData,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // If we should write back the cache after request.\n        if (populateCache) {\n            if (!isError) {\n                // Transform the result into data.\n                if (isFunction(populateCache)) {\n                    const populateCachedData = populateCache(data, committedData);\n                    set({\n                        data: populateCachedData,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                } else {\n                    // Only update cached data and reset the error if there's no error. Data can be `undefined` here.\n                    set({\n                        data,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                }\n            }\n        }\n        // Reset the timestamp to mark the mutation has ended.\n        MUTATION[key][1] = getTimestamp();\n        // Update existing SWR Hooks' internal states:\n        Promise.resolve(startRevalidate()).then(()=>{\n            // The mutation and revalidation are ended, we can clear it since the data is\n            // not an optimistic value anymore.\n            set({\n                _c: UNDEFINED\n            });\n        });\n        // Throw error or return data\n        if (isError) {\n            if (throwOnError) throw error;\n            return;\n        }\n        return data;\n    }\n}\nconst revalidateAllKeys = (revalidators, type)=>{\n    for(const key in revalidators){\n        if (revalidators[key][0]) revalidators[key][0](type);\n    }\n};\nconst initCache = (provider, options)=>{\n    // The global state for a specific provider will be used to deduplicate\n    // requests and store listeners. As well as a mutate function that is bound to\n    // the cache.\n    // The provider's global state might be already initialized. Let's try to get the\n    // global state associated with the provider first.\n    if (!SWRGlobalState.has(provider)) {\n        const opts = mergeObjects(defaultConfigOptions, options);\n        // If there's no global state bound to the provider, create a new one with the\n        // new mutate function.\n        const EVENT_REVALIDATORS = Object.create(null);\n        const mutate = internalMutate.bind(UNDEFINED, provider);\n        let unmount = noop;\n        const subscriptions = Object.create(null);\n        const subscribe = (key, callback)=>{\n            const subs = subscriptions[key] || [];\n            subscriptions[key] = subs;\n            subs.push(callback);\n            return ()=>subs.splice(subs.indexOf(callback), 1);\n        };\n        const setter = (key, value, prev)=>{\n            provider.set(key, value);\n            const subs = subscriptions[key];\n            if (subs) {\n                for (const fn of subs){\n                    fn(value, prev);\n                }\n            }\n        };\n        const initProvider = ()=>{\n            if (!SWRGlobalState.has(provider)) {\n                // Update the state if it's new, or if the provider has been extended.\n                SWRGlobalState.set(provider, [\n                    EVENT_REVALIDATORS,\n                    Object.create(null),\n                    Object.create(null),\n                    Object.create(null),\n                    mutate,\n                    setter,\n                    subscribe\n                ]);\n                if (!IS_SERVER) {\n                    // When listening to the native events for auto revalidations,\n                    // we intentionally put a delay (setTimeout) here to make sure they are\n                    // fired after immediate JavaScript executions, which can be\n                    // React's state updates.\n                    // This avoids some unnecessary revalidations such as\n                    // https://github.com/vercel/swr/issues/1680.\n                    const releaseFocus = opts.initFocus(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, _events_mjs__WEBPACK_IMPORTED_MODULE_2__.FOCUS_EVENT)));\n                    const releaseReconnect = opts.initReconnect(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, _events_mjs__WEBPACK_IMPORTED_MODULE_2__.RECONNECT_EVENT)));\n                    unmount = ()=>{\n                        releaseFocus && releaseFocus();\n                        releaseReconnect && releaseReconnect();\n                        // When un-mounting, we need to remove the cache provider from the state\n                        // storage too because it's a side-effect. Otherwise, when re-mounting we\n                        // will not re-register those event listeners.\n                        SWRGlobalState.delete(provider);\n                    };\n                }\n            }\n        };\n        initProvider();\n        // This is a new provider, we need to initialize it and setup DOM events\n        // listeners for `focus` and `reconnect` actions.\n        // We might want to inject an extra layer on top of `provider` in the future,\n        // such as key serialization, auto GC, etc.\n        // For now, it's just a `Map` interface without any modifications.\n        return [\n            provider,\n            mutate,\n            initProvider,\n            unmount\n        ];\n    }\n    return [\n        provider,\n        SWRGlobalState.get(provider)[4]\n    ];\n};\n// error retry\nconst onErrorRetry = (_, __, config, revalidate, opts)=>{\n    const maxRetryCount = config.errorRetryCount;\n    const currentRetryCount = opts.retryCount;\n    // Exponential backoff\n    const timeout = ~~((Math.random() + 0.5) * (1 << (currentRetryCount < 8 ? currentRetryCount : 8))) * config.errorRetryInterval;\n    if (!isUndefined(maxRetryCount) && currentRetryCount > maxRetryCount) {\n        return;\n    }\n    setTimeout(revalidate, timeout, opts);\n};\nconst compare = dequal_lite__WEBPACK_IMPORTED_MODULE_1__.dequal;\n// Default cache provider\nconst [cache, mutate] = initCache(new Map());\n// Default config\nconst defaultConfig = mergeObjects({\n    // events\n    onLoadingSlow: noop,\n    onSuccess: noop,\n    onError: noop,\n    onErrorRetry,\n    onDiscarded: noop,\n    // switches\n    revalidateOnFocus: true,\n    revalidateOnReconnect: true,\n    revalidateIfStale: true,\n    shouldRetryOnError: true,\n    // timeouts\n    errorRetryInterval: slowConnection ? 10000 : 5000,\n    focusThrottleInterval: 5 * 1000,\n    dedupingInterval: 2 * 1000,\n    loadingTimeout: slowConnection ? 5000 : 3000,\n    // providers\n    compare,\n    isPaused: ()=>false,\n    cache,\n    mutate,\n    fallback: {}\n}, preset);\nconst mergeConfigs = (a, b)=>{\n    // Need to create a new object to avoid mutating the original here.\n    const v = mergeObjects(a, b);\n    // If two configs are provided, merge their `use` and `fallback` options.\n    if (b) {\n        const { use: u1, fallback: f1 } = a;\n        const { use: u2, fallback: f2 } = b;\n        if (u1 && u2) {\n            v.use = u1.concat(u2);\n        }\n        if (f1 && f2) {\n            v.fallback = mergeObjects(f1, f2);\n        }\n    }\n    return v;\n};\nconst SWRConfigContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({});\nconst SWRConfig = (props)=>{\n    const { value } = props;\n    const parentConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SWRConfigContext);\n    const isFunctionalConfig = isFunction(value);\n    const config = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"SWRConfig.useMemo[config]\": ()=>isFunctionalConfig ? value(parentConfig) : value\n    }[\"SWRConfig.useMemo[config]\"], [\n        isFunctionalConfig,\n        parentConfig,\n        value\n    ]);\n    // Extend parent context values and middleware.\n    const extendedConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"SWRConfig.useMemo[extendedConfig]\": ()=>isFunctionalConfig ? config : mergeConfigs(parentConfig, config)\n    }[\"SWRConfig.useMemo[extendedConfig]\"], [\n        isFunctionalConfig,\n        parentConfig,\n        config\n    ]);\n    // Should not use the inherited provider.\n    const provider = config && config.provider;\n    // initialize the cache only on first access.\n    const cacheContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(UNDEFINED);\n    if (provider && !cacheContextRef.current) {\n        cacheContextRef.current = initCache(provider(extendedConfig.cache || cache), config);\n    }\n    const cacheContext = cacheContextRef.current;\n    // Override the cache if a new provider is given.\n    if (cacheContext) {\n        extendedConfig.cache = cacheContext[0];\n        extendedConfig.mutate = cacheContext[1];\n    }\n    // Unsubscribe events.\n    useIsomorphicLayoutEffect({\n        \"SWRConfig.useIsomorphicLayoutEffect\": ()=>{\n            if (cacheContext) {\n                cacheContext[2] && cacheContext[2]();\n                return cacheContext[3];\n            }\n        }\n    }[\"SWRConfig.useIsomorphicLayoutEffect\"], []);\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(SWRConfigContext.Provider, mergeObjects(props, {\n        value: extendedConfig\n    }));\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/constants.mjs":
/*!*************************************************************************************************!*\
  !*** ./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/constants.mjs ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INFINITE_PREFIX: () => (/* binding */ INFINITE_PREFIX)\n/* harmony export */ });\nconst INFINITE_PREFIX = '$inf$';\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vc3dyQDIuMy40X3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvc3dyL2Rpc3QvX2ludGVybmFsL2NvbnN0YW50cy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUUyQiIsInNvdXJjZXMiOlsiL1VzZXJzL21lbWltYWwvUHljaGFybVByb2plY3RzL2JldGJldC1wbGF0Zm9ybS9mcm9udGVuZC93ZWIvbm9kZV9tb2R1bGVzLy5wbnBtL3N3ckAyLjMuNF9yZWFjdEAxOS4xLjEvbm9kZV9tb2R1bGVzL3N3ci9kaXN0L19pbnRlcm5hbC9jb25zdGFudHMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IElORklOSVRFX1BSRUZJWCA9ICckaW5mJCc7XG5cbmV4cG9ydCB7IElORklOSVRFX1BSRUZJWCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/constants.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/events.mjs":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/events.mjs ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ERROR_REVALIDATE_EVENT: () => (/* binding */ ERROR_REVALIDATE_EVENT),\n/* harmony export */   FOCUS_EVENT: () => (/* binding */ FOCUS_EVENT),\n/* harmony export */   MUTATE_EVENT: () => (/* binding */ MUTATE_EVENT),\n/* harmony export */   RECONNECT_EVENT: () => (/* binding */ RECONNECT_EVENT)\n/* harmony export */ });\nconst FOCUS_EVENT = 0;\nconst RECONNECT_EVENT = 1;\nconst MUTATE_EVENT = 2;\nconst ERROR_REVALIDATE_EVENT = 3;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vc3dyQDIuMy40X3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvc3dyL2Rpc3QvX2ludGVybmFsL2V2ZW50cy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUU4RSIsInNvdXJjZXMiOlsiL1VzZXJzL21lbWltYWwvUHljaGFybVByb2plY3RzL2JldGJldC1wbGF0Zm9ybS9mcm9udGVuZC93ZWIvbm9kZV9tb2R1bGVzLy5wbnBtL3N3ckAyLjMuNF9yZWFjdEAxOS4xLjEvbm9kZV9tb2R1bGVzL3N3ci9kaXN0L19pbnRlcm5hbC9ldmVudHMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IEZPQ1VTX0VWRU5UID0gMDtcbmNvbnN0IFJFQ09OTkVDVF9FVkVOVCA9IDE7XG5jb25zdCBNVVRBVEVfRVZFTlQgPSAyO1xuY29uc3QgRVJST1JfUkVWQUxJREFURV9FVkVOVCA9IDM7XG5cbmV4cG9ydCB7IEVSUk9SX1JFVkFMSURBVEVfRVZFTlQsIEZPQ1VTX0VWRU5ULCBNVVRBVEVfRVZFTlQsIFJFQ09OTkVDVF9FVkVOVCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/events.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/index.mjs":
/*!*********************************************************************************************!*\
  !*** ./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/index.mjs ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INFINITE_PREFIX: () => (/* reexport safe */ _constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX),\n/* harmony export */   IS_REACT_LEGACY: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.I),\n/* harmony export */   IS_SERVER: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.r),\n/* harmony export */   OBJECT: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.O),\n/* harmony export */   SWRConfig: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.g),\n/* harmony export */   SWRGlobalState: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.b),\n/* harmony export */   UNDEFINED: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.U),\n/* harmony export */   cache: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   compare: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.k),\n/* harmony export */   createCacheHelper: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.z),\n/* harmony export */   defaultConfig: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   defaultConfigOptions: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.q),\n/* harmony export */   getTimestamp: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.o),\n/* harmony export */   hasRequestAnimationFrame: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.y),\n/* harmony export */   initCache: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.h),\n/* harmony export */   internalMutate: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.n),\n/* harmony export */   isDocumentDefined: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.w),\n/* harmony export */   isFunction: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.a),\n/* harmony export */   isLegacyDeno: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.x),\n/* harmony export */   isPromiseLike: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.B),\n/* harmony export */   isUndefined: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.e),\n/* harmony export */   isWindowDefined: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.i),\n/* harmony export */   mergeConfigs: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   mergeObjects: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.m),\n/* harmony export */   mutate: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   noop: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.A),\n/* harmony export */   normalize: () => (/* binding */ normalize),\n/* harmony export */   preload: () => (/* binding */ preload),\n/* harmony export */   preset: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   rAF: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.t),\n/* harmony export */   revalidateEvents: () => (/* reexport module object */ _events_mjs__WEBPACK_IMPORTED_MODULE_1__),\n/* harmony export */   serialize: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.s),\n/* harmony export */   slowConnection: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.v),\n/* harmony export */   stableHash: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.l),\n/* harmony export */   subscribeCallback: () => (/* binding */ subscribeCallback),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.u),\n/* harmony export */   useSWRConfig: () => (/* binding */ useSWRConfig),\n/* harmony export */   withArgs: () => (/* binding */ withArgs),\n/* harmony export */   withMiddleware: () => (/* binding */ withMiddleware)\n/* harmony export */ });\n/* harmony import */ var _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config-context-client-BoS53ST9.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs\");\n/* harmony import */ var _events_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./events.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var _constants_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/constants.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n\n\n\n\n// @ts-expect-error\nconst enableDevtools = _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.i && window.__SWR_DEVTOOLS_USE__;\nconst use = enableDevtools ? window.__SWR_DEVTOOLS_USE__ : [];\nconst setupDevTools = ()=>{\n    if (enableDevtools) {\n        // @ts-expect-error\n        window.__SWR_DEVTOOLS_REACT__ = react__WEBPACK_IMPORTED_MODULE_3__;\n    }\n};\n\nconst normalize = (args)=>{\n    return (0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(args[1]) ? [\n        args[0],\n        args[1],\n        args[2] || {}\n    ] : [\n        args[0],\n        null,\n        (args[1] === null ? args[2] : args[1]) || {}\n    ];\n};\n\nconst useSWRConfig = ()=>{\n    return (0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.m)(_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.d, (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.S));\n};\n\nconst preload = (key_, fetcher)=>{\n    const [key, fnArg] = (0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.s)(key_);\n    const [, , , PRELOAD] = _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.b.get(_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.c);\n    // Prevent preload to be called multiple times before used.\n    if (PRELOAD[key]) return PRELOAD[key];\n    const req = fetcher(fnArg);\n    PRELOAD[key] = req;\n    return req;\n};\nconst middleware = (useSWRNext)=>(key_, fetcher_, config)=>{\n        // fetcher might be a sync function, so this should not be an async function\n        const fetcher = fetcher_ && ((...args)=>{\n            const [key] = (0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.s)(key_);\n            const [, , , PRELOAD] = _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.b.get(_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.c);\n            if (key.startsWith(_constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX)) {\n                // we want the infinite fetcher to be called.\n                // handling of the PRELOAD cache happens there.\n                return fetcher_(...args);\n            }\n            const req = PRELOAD[key];\n            if ((0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.e)(req)) return fetcher_(...args);\n            delete PRELOAD[key];\n            return req;\n        });\n        return useSWRNext(key_, fetcher, config);\n    };\n\nconst BUILT_IN_MIDDLEWARE = use.concat(middleware);\n\n// It's tricky to pass generic types as parameters, so we just directly override\n// the types here.\nconst withArgs = (hook)=>{\n    return function useSWRArgs(...args) {\n        // Get the default and inherited configuration.\n        const fallbackConfig = useSWRConfig();\n        // Normalize arguments.\n        const [key, fn, _config] = normalize(args);\n        // Merge configurations.\n        const config = (0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.f)(fallbackConfig, _config);\n        // Apply middleware\n        let next = hook;\n        const { use } = config;\n        const middleware = (use || []).concat(BUILT_IN_MIDDLEWARE);\n        for(let i = middleware.length; i--;){\n            next = middleware[i](next);\n        }\n        return next(key, fn || config.fetcher || null, config);\n    };\n};\n\n// Add a callback function to a list of keyed callback functions and return\n// the unsubscribe function.\nconst subscribeCallback = (key, callbacks, callback)=>{\n    const keyedRevalidators = callbacks[key] || (callbacks[key] = []);\n    keyedRevalidators.push(callback);\n    return ()=>{\n        const index = keyedRevalidators.indexOf(callback);\n        if (index >= 0) {\n            // O(1): faster than splice\n            keyedRevalidators[index] = keyedRevalidators[keyedRevalidators.length - 1];\n            keyedRevalidators.pop();\n        }\n    };\n};\n\n// Create a custom hook with a middleware\nconst withMiddleware = (useSWR, middleware)=>{\n    return (...args)=>{\n        const [key, fn, config] = normalize(args);\n        const uses = (config.use || []).concat(middleware);\n        return useSWR(key, fn, {\n            ...config,\n            use: uses\n        });\n    };\n};\n\nsetupDevTools();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/index/index.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/index/index.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SWRConfig: () => (/* binding */ SWRConfig),\n/* harmony export */   \"default\": () => (/* binding */ useSWR),\n/* harmony export */   mutate: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.j),\n/* harmony export */   preload: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.preload),\n/* harmony export */   unstable_serialize: () => (/* binding */ unstable_serialize),\n/* harmony export */   useSWRConfig: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.useSWRConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.1/node_modules/use-sync-external-store/shim/index.js\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/index.mjs\");\n\n\n\n\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst getTypeName = (value)=>OBJECT.prototype.toString.call(value);\nconst isObjectTypeName = (typeName, type)=>typeName === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const typeName = getTypeName(arg);\n    const isDate = isObjectTypeName(typeName, 'Date');\n    const isRegex = isObjectTypeName(typeName, 'RegExp');\n    const isPlainObject = isObjectTypeName(typeName, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\nconst unstable_serialize = (key)=>serialize(key)[0];\n\n/// <reference types=\"react/experimental\" />\nconst use = react__WEBPACK_IMPORTED_MODULE_0__.use || // This extra generic is to avoid TypeScript mixing up the generic and JSX sytax\n// and emitting an error.\n// We assume that this is only for the `use(thenable)` case, not `use(context)`.\n// https://github.com/facebook/react/blob/aed00dacfb79d17c53218404c52b1c7aa59c4a89/packages/react-server/src/ReactFizzThenable.js#L45\n((thenable)=>{\n    switch(thenable.status){\n        case 'pending':\n            throw thenable;\n        case 'fulfilled':\n            return thenable.value;\n        case 'rejected':\n            throw thenable.reason;\n        default:\n            thenable.status = 'pending';\n            thenable.then((v)=>{\n                thenable.status = 'fulfilled';\n                thenable.value = v;\n            }, (e)=>{\n                thenable.status = 'rejected';\n                thenable.reason = e;\n            });\n            throw thenable;\n    }\n});\nconst WITH_DEDUPE = {\n    dedupe: true\n};\nconst useSWRHandler = (_key, fetcher, config)=>{\n    const { cache, compare, suspense, fallbackData, revalidateOnMount, revalidateIfStale, refreshInterval, refreshWhenHidden, refreshWhenOffline, keepPreviousData } = config;\n    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.b.get(cache);\n    // `key` is the identifier of the SWR internal state,\n    // `fnArg` is the argument/arguments parsed from the key, which will be passed\n    // to the fetcher.\n    // All of them are derived from `_key`.\n    const [key, fnArg] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.s)(_key);\n    // If it's the initial render of this hook.\n    const initialMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // If the hook is unmounted already. This will be used to prevent some effects\n    // to be called after unmounting.\n    const unmountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Refs to keep the key and config.\n    const keyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(key);\n    const fetcherRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(fetcher);\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(config);\n    const getConfig = ()=>configRef.current;\n    const isActive = ()=>getConfig().isVisible() && getConfig().isOnline();\n    const [getCache, setCache, subscribeCache, getInitialCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.z)(cache, key);\n    const stateDependencies = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({}).current;\n    // Resolve the fallback data from either the inline option, or the global provider.\n    // If it's a promise, we simply let React suspend and resolve it for us.\n    const fallback = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(fallbackData) ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(config.fallback) ? _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U : config.fallback[key] : fallbackData;\n    const isEqual = (prev, current)=>{\n        for(const _ in stateDependencies){\n            const t = _;\n            if (t === 'data') {\n                if (!compare(prev[t], current[t])) {\n                    if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(prev[t])) {\n                        return false;\n                    }\n                    if (!compare(returnedData, current[t])) {\n                        return false;\n                    }\n                }\n            } else {\n                if (current[t] !== prev[t]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    const getSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const shouldStartRequest = (()=>{\n            if (!key) return false;\n            if (!fetcher) return false;\n            // If `revalidateOnMount` is set, we take the value directly.\n            if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(revalidateOnMount)) return revalidateOnMount;\n            // If it's paused, we skip revalidation.\n            if (getConfig().isPaused()) return false;\n            if (suspense) return false;\n            return revalidateIfStale !== false;\n        })();\n        // Get the cache and merge it with expected states.\n        const getSelectedCache = (state)=>{\n            // We only select the needed fields from the state.\n            const snapshot = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.m)(state);\n            delete snapshot._k;\n            if (!shouldStartRequest) {\n                return snapshot;\n            }\n            return {\n                isValidating: true,\n                isLoading: true,\n                ...snapshot\n            };\n        };\n        const cachedData = getCache();\n        const initialData = getInitialCache();\n        const clientSnapshot = getSelectedCache(cachedData);\n        const serverSnapshot = cachedData === initialData ? clientSnapshot : getSelectedCache(initialData);\n        // To make sure that we are returning the same object reference to avoid\n        // unnecessary re-renders, we keep the previous snapshot and use deep\n        // comparison to check if we need to return a new one.\n        let memorizedSnapshot = clientSnapshot;\n        return [\n            ()=>{\n                const newSnapshot = getSelectedCache(getCache());\n                const compareResult = isEqual(newSnapshot, memorizedSnapshot);\n                if (compareResult) {\n                    // Mentally, we should always return the `memorizedSnapshot` here\n                    // as there's no change between the new and old snapshots.\n                    // However, since the `isEqual` function only compares selected fields,\n                    // the values of the unselected fields might be changed. That's\n                    // simply because we didn't track them.\n                    // To support the case in https://github.com/vercel/swr/pull/2576,\n                    // we need to update these fields in the `memorizedSnapshot` too\n                    // with direct mutations to ensure the snapshot is always up-to-date\n                    // even for the unselected fields, but only trigger re-renders when\n                    // the selected fields are changed.\n                    memorizedSnapshot.data = newSnapshot.data;\n                    memorizedSnapshot.isLoading = newSnapshot.isLoading;\n                    memorizedSnapshot.isValidating = newSnapshot.isValidating;\n                    memorizedSnapshot.error = newSnapshot.error;\n                    return memorizedSnapshot;\n                } else {\n                    memorizedSnapshot = newSnapshot;\n                    return newSnapshot;\n                }\n            },\n            ()=>serverSnapshot\n        ];\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        cache,\n        key\n    ]);\n    // Get the current state that SWR should return.\n    const cached = (0,use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore)((0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((callback)=>subscribeCache(key, (current, prev)=>{\n            if (!isEqual(prev, current)) callback();\n        }), // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        cache,\n        key\n    ]), getSnapshot[0], getSnapshot[1]);\n    const isInitialMount = !initialMountedRef.current;\n    const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;\n    const cachedData = cached.data;\n    const data = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData) ? fallback && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.B)(fallback) ? use(fallback) : fallback : cachedData;\n    const error = cached.error;\n    // Use a ref to store previously returned data. Use the initial data as its initial value.\n    const laggyDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(data);\n    const returnedData = keepPreviousData ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData) ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(laggyDataRef.current) ? data : laggyDataRef.current : cachedData : data;\n    // - Suspense mode and there's stale data for the initial render.\n    // - Not suspense mode and there is no fallback data and `revalidateIfStale` is enabled.\n    // - `revalidateIfStale` is enabled but `data` is not defined.\n    const shouldDoInitialRevalidation = (()=>{\n        // if a key already has revalidators and also has error, we should not trigger revalidation\n        if (hasRevalidator && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(error)) return false;\n        // If `revalidateOnMount` is set, we take the value directly.\n        if (isInitialMount && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(revalidateOnMount)) return revalidateOnMount;\n        // If it's paused, we skip revalidation.\n        if (getConfig().isPaused()) return false;\n        // Under suspense mode, it will always fetch on render if there is no\n        // stale data so no need to revalidate immediately mount it again.\n        // If data exists, only revalidate if `revalidateIfStale` is true.\n        if (suspense) return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) ? false : revalidateIfStale;\n        // If there is no stale data, we need to revalidate when mount;\n        // If `revalidateIfStale` is set to true, we will always revalidate.\n        return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) || revalidateIfStale;\n    })();\n    // Resolve the default validating state:\n    // If it's able to validate, and it should revalidate when mount, this will be true.\n    const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);\n    const isValidating = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cached.isValidating) ? defaultValidatingState : cached.isValidating;\n    const isLoading = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cached.isLoading) ? defaultValidatingState : cached.isLoading;\n    // The revalidation function is a carefully crafted wrapper of the original\n    // `fetcher`, to correctly handle the many edge cases.\n    const revalidate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (revalidateOpts)=>{\n        const currentFetcher = fetcherRef.current;\n        if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {\n            return false;\n        }\n        let newData;\n        let startAt;\n        let loading = true;\n        const opts = revalidateOpts || {};\n        // If there is no ongoing concurrent request, or `dedupe` is not set, a\n        // new request should be initiated.\n        const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;\n        /*\n         For React 17\n         Do unmount check for calls:\n         If key has changed during the revalidation, or the component has been\n         unmounted, old dispatch and old event callbacks should not take any\n         effect\n\n        For React 18\n        only check if key has changed\n        https://github.com/reactwg/react-18/discussions/82\n      */ const callbackSafeguard = ()=>{\n            if (_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.I) {\n                return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;\n            }\n            return key === keyRef.current;\n        };\n        // The final state object when the request finishes.\n        const finalState = {\n            isValidating: false,\n            isLoading: false\n        };\n        const finishRequestAndUpdateState = ()=>{\n            setCache(finalState);\n        };\n        const cleanupState = ()=>{\n            // Check if it's still the same request before deleting it.\n            const requestInfo = FETCH[key];\n            if (requestInfo && requestInfo[1] === startAt) {\n                delete FETCH[key];\n            }\n        };\n        // Start fetching. Change the `isValidating` state, update the cache.\n        const initialState = {\n            isValidating: true\n        };\n        // It is in the `isLoading` state, if and only if there is no cached data.\n        // This bypasses fallback data and laggy data.\n        if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(getCache().data)) {\n            initialState.isLoading = true;\n        }\n        try {\n            if (shouldStartNewRequest) {\n                setCache(initialState);\n                // If no cache is being rendered currently (it shows a blank page),\n                // we trigger the loading slow event.\n                if (config.loadingTimeout && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(getCache().data)) {\n                    setTimeout(()=>{\n                        if (loading && callbackSafeguard()) {\n                            getConfig().onLoadingSlow(key, config);\n                        }\n                    }, config.loadingTimeout);\n                }\n                // Start the request and save the timestamp.\n                // Key must be truthy if entering here.\n                FETCH[key] = [\n                    currentFetcher(fnArg),\n                    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.o)()\n                ];\n            }\n            // Wait until the ongoing request is done. Deduplication is also\n            // considered here.\n            ;\n            [newData, startAt] = FETCH[key];\n            newData = await newData;\n            if (shouldStartNewRequest) {\n                // If the request isn't interrupted, clean it up after the\n                // deduplication interval.\n                setTimeout(cleanupState, config.dedupingInterval);\n            }\n            // If there're other ongoing request(s), started after the current one,\n            // we need to ignore the current one to avoid possible race conditions:\n            //   req1------------------>res1        (current one)\n            //        req2---------------->res2\n            // the request that fired later will always be kept.\n            // The timestamp maybe be `undefined` or a number\n            if (!FETCH[key] || FETCH[key][1] !== startAt) {\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Clear error.\n            finalState.error = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U;\n            // If there're other mutations(s), that overlapped with the current revalidation:\n            // case 1:\n            //   req------------------>res\n            //       mutate------>end\n            // case 2:\n            //         req------------>res\n            //   mutate------>end\n            // case 3:\n            //   req------------------>res\n            //       mutate-------...---------->\n            // we have to ignore the revalidation result (res) because it's no longer fresh.\n            // meanwhile, a new revalidation should be triggered when the mutation ends.\n            const mutationInfo = MUTATION[key];\n            if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(mutationInfo) && // case 1\n            (startAt <= mutationInfo[0] || // case 2\n            startAt <= mutationInfo[1] || // case 3\n            mutationInfo[1] === 0)) {\n                finishRequestAndUpdateState();\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Deep compare with the latest state to avoid extra re-renders.\n            // For local state, compare and assign.\n            const cacheData = getCache().data;\n            // Since the compare fn could be custom fn\n            // cacheData might be different from newData even when compare fn returns True\n            finalState.data = compare(cacheData, newData) ? cacheData : newData;\n            // Trigger the successful callback if it's the original request.\n            if (shouldStartNewRequest) {\n                if (callbackSafeguard()) {\n                    getConfig().onSuccess(newData, key, config);\n                }\n            }\n        } catch (err) {\n            cleanupState();\n            const currentConfig = getConfig();\n            const { shouldRetryOnError } = currentConfig;\n            // Not paused, we continue handling the error. Otherwise, discard it.\n            if (!currentConfig.isPaused()) {\n                // Get a new error, don't use deep comparison for errors.\n                finalState.error = err;\n                // Error event and retry logic. Only for the actual request, not\n                // deduped ones.\n                if (shouldStartNewRequest && callbackSafeguard()) {\n                    currentConfig.onError(err, key, currentConfig);\n                    if (shouldRetryOnError === true || (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.a)(shouldRetryOnError) && shouldRetryOnError(err)) {\n                        if (!getConfig().revalidateOnFocus || !getConfig().revalidateOnReconnect || isActive()) {\n                            // If it's inactive, stop. It will auto-revalidate when\n                            // refocusing or reconnecting.\n                            // When retrying, deduplication is always enabled.\n                            currentConfig.onErrorRetry(err, key, currentConfig, (_opts)=>{\n                                const revalidators = EVENT_REVALIDATORS[key];\n                                if (revalidators && revalidators[0]) {\n                                    revalidators[0](_internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.ERROR_REVALIDATE_EVENT, _opts);\n                                }\n                            }, {\n                                retryCount: (opts.retryCount || 0) + 1,\n                                dedupe: true\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Mark loading as stopped.\n        loading = false;\n        // Update the current hook's state.\n        finishRequestAndUpdateState();\n        return true;\n    }, // `setState` is immutable, and `eventsCallback`, `fnArg`, and\n    // `keyValidating` are depending on `key`, so we can exclude them from\n    // the deps array.\n    //\n    // FIXME:\n    // `fn` and `config` might be changed during the lifecycle,\n    // but they might be changed every render like this.\n    // `useSWR('key', () => fetch('/api/'), { suspense: true })`\n    // So we omit the values from the deps array\n    // even though it might cause unexpected behaviors.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        key,\n        cache\n    ]);\n    // Similar to the global mutate but bound to the current cache and key.\n    // `cache` isn't allowed to change during the lifecycle.\n    const boundMutate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(// Use callback to make sure `keyRef.current` returns latest result every time\n    (...args)=>{\n        return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.n)(cache, keyRef.current, ...args);\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // The logic for updating refs.\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        // Handle laggy data updates. If there's cached data of the current key,\n        // it'll be the correct reference.\n        if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData)) {\n            laggyDataRef.current = cachedData;\n        }\n    });\n    // After mounted or key changed.\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        if (!key) return;\n        const softRevalidate = revalidate.bind(_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U, WITH_DEDUPE);\n        let nextFocusRevalidatedAt = 0;\n        if (getConfig().revalidateOnFocus) {\n            const initNow = Date.now();\n            nextFocusRevalidatedAt = initNow + getConfig().focusThrottleInterval;\n        }\n        // Expose revalidators to global event listeners. So we can trigger\n        // revalidation from the outside.\n        const onRevalidate = (type, opts = {})=>{\n            if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.FOCUS_EVENT) {\n                const now = Date.now();\n                if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {\n                    nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;\n                    softRevalidate();\n                }\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.RECONNECT_EVENT) {\n                if (getConfig().revalidateOnReconnect && isActive()) {\n                    softRevalidate();\n                }\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.MUTATE_EVENT) {\n                return revalidate();\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.ERROR_REVALIDATE_EVENT) {\n                return revalidate(opts);\n            }\n            return;\n        };\n        const unsubEvents = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.subscribeCallback)(key, EVENT_REVALIDATORS, onRevalidate);\n        // Mark the component as mounted and update corresponding refs.\n        unmountedRef.current = false;\n        keyRef.current = key;\n        initialMountedRef.current = true;\n        // Keep the original key in the cache.\n        setCache({\n            _k: fnArg\n        });\n        // Trigger a revalidation\n        if (shouldDoInitialRevalidation) {\n            // Performance optimization: if a request is already in progress for this key,\n            // skip the revalidation to avoid redundant work\n            if (!FETCH[key]) {\n                if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) || _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.r) {\n                    // Revalidate immediately.\n                    softRevalidate();\n                } else {\n                    // Delay the revalidate if we have data to return so we won't block\n                    // rendering.\n                    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.t)(softRevalidate);\n                }\n            }\n        }\n        return ()=>{\n            // Mark it as unmounted.\n            unmountedRef.current = true;\n            unsubEvents();\n        };\n    }, [\n        key\n    ]);\n    // Polling\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        let timer;\n        function next() {\n            // Use the passed interval\n            // ...or invoke the function with the updated data to get the interval\n            const interval = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.a)(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;\n            // We only start the next interval if `refreshInterval` is not 0, and:\n            // - `force` is true, which is the start of polling\n            // - or `timer` is not 0, which means the effect wasn't canceled\n            if (interval && timer !== -1) {\n                timer = setTimeout(execute, interval);\n            }\n        }\n        function execute() {\n            // Check if it's OK to execute:\n            // Only revalidate when the page is visible, online, and not errored.\n            if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {\n                revalidate(WITH_DEDUPE).then(next);\n            } else {\n                // Schedule the next interval to check again.\n                next();\n            }\n        }\n        next();\n        return ()=>{\n            if (timer) {\n                clearTimeout(timer);\n                timer = -1;\n            }\n        };\n    }, [\n        refreshInterval,\n        refreshWhenHidden,\n        refreshWhenOffline,\n        key\n    ]);\n    // Display debug info in React DevTools.\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(returnedData);\n    // In Suspense mode, we can't return the empty `data` state.\n    // If there is an `error`, the `error` needs to be thrown to the error boundary.\n    // If there is no `error`, the `revalidation` promise needs to be thrown to\n    // the suspense boundary.\n    if (suspense && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) && key) {\n        // SWR should throw when trying to use Suspense on the server with React 18,\n        // without providing any fallback data. This causes hydration errors. See:\n        // https://github.com/vercel/swr/issues/1832\n        if (!_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.I && _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.r) {\n            throw new Error('Fallback data is required when using Suspense in SSR.');\n        }\n        // Always update fetcher and config refs even with the Suspense mode.\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        unmountedRef.current = false;\n        const req = PRELOAD[key];\n        if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(req)) {\n            const promise = boundMutate(req);\n            use(promise);\n        }\n        if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(error)) {\n            const promise = revalidate(WITH_DEDUPE);\n            if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(returnedData)) {\n                promise.status = 'fulfilled';\n                promise.value = true;\n            }\n            use(promise);\n        } else {\n            throw error;\n        }\n    }\n    const swrResponse = {\n        mutate: boundMutate,\n        get data () {\n            stateDependencies.data = true;\n            return returnedData;\n        },\n        get error () {\n            stateDependencies.error = true;\n            return error;\n        },\n        get isValidating () {\n            stateDependencies.isValidating = true;\n            return isValidating;\n        },\n        get isLoading () {\n            stateDependencies.isLoading = true;\n            return isLoading;\n        }\n    };\n    return swrResponse;\n};\nconst SWRConfig = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.O.defineProperty(_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.g, 'defaultValue', {\n    value: _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.d\n});\n/**\n * A hook to fetch data.\n *\n * @link https://swr.vercel.app\n * @example\n * ```jsx\n * import useSWR from 'swr'\n * function Profile() {\n *   const { data, error, isLoading } = useSWR('/api/user', fetcher)\n *   if (error) return <div>failed to load</div>\n *   if (isLoading) return <div>loading...</div>\n *   return <div>hello {data.name}!</div>\n * }\n * ```\n */ const useSWR = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.withArgs)(useSWRHandler);\n\n// useSWR\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/index/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/infinite/index.mjs":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/infinite/index.mjs ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSWRInfinite),\n/* harmony export */   infinite: () => (/* binding */ infinite),\n/* harmony export */   unstable_serialize: () => (/* binding */ unstable_serialize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _index_index_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../index/index.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/index/index.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs\");\n/* harmony import */ var _internal_constants_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_internal/constants.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/constants.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/index.mjs\");\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.1/node_modules/use-sync-external-store/shim/index.js\");\n\n\n\n\n\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst getTypeName = (value)=>OBJECT.prototype.toString.call(value);\nconst isObjectTypeName = (typeName, type)=>typeName === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const typeName = getTypeName(arg);\n    const isDate = isObjectTypeName(typeName, 'Date');\n    const isRegex = isObjectTypeName(typeName, 'RegExp');\n    const isPlainObject = isObjectTypeName(typeName, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\nconst getFirstPageKey = (getKey)=>{\n    return serialize(getKey ? getKey(0, null) : null)[0];\n};\nconst unstable_serialize = (getKey)=>{\n    return _internal_constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX + getFirstPageKey(getKey);\n};\n\n// We have to several type castings here because `useSWRInfinite` is a special\n// hook where `key` and return type are not like the normal `useSWR` types.\nconst EMPTY_PROMISE = Promise.resolve();\nconst infinite = (useSWRNext)=>(getKey, fn, config)=>{\n        const didMountRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n        const { cache: cache$1, initialSize = 1, revalidateAll = false, persistSize = false, revalidateFirstPage = true, revalidateOnMount = false, parallel = false } = config;\n        const [, , , PRELOAD] = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.b.get(_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.c);\n        // The serialized key of the first page. This key will be used to store\n        // metadata of this SWR infinite hook.\n        let infiniteKey;\n        try {\n            infiniteKey = getFirstPageKey(getKey);\n            if (infiniteKey) infiniteKey = _internal_constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX + infiniteKey;\n        } catch (err) {\n        // Not ready yet.\n        }\n        const [get, set, subscribeCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, infiniteKey);\n        const getSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n            const size = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(get()._l) ? initialSize : get()._l;\n            return size;\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            cache$1,\n            infiniteKey,\n            initialSize\n        ]);\n        (0,use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore)((0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((callback)=>{\n            if (infiniteKey) return subscribeCache(infiniteKey, ()=>{\n                callback();\n            });\n            return ()=>{};\n        }, // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            cache$1,\n            infiniteKey\n        ]), getSnapshot, getSnapshot);\n        const resolvePageSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n            const cachedPageSize = get()._l;\n            return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(cachedPageSize) ? initialSize : cachedPageSize;\n        // `cache` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            initialSize\n        ]);\n        // keep the last page size to restore it with the persistSize option\n        const lastPageSizeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(resolvePageSize());\n        // When the page key changes, we reset the page size if it's not persisted\n        (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.u)(()=>{\n            if (!didMountRef.current) {\n                didMountRef.current = true;\n                return;\n            }\n            if (infiniteKey) {\n                // If the key has been changed, we keep the current page size if persistSize is enabled\n                // Otherwise, we reset the page size to cached pageSize\n                set({\n                    _l: persistSize ? lastPageSizeRef.current : resolvePageSize()\n                });\n            }\n        // `initialSize` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            cache$1\n        ]);\n        // Needs to check didMountRef during mounting, not in the fetcher\n        const shouldRevalidateOnMount = revalidateOnMount && !didMountRef.current;\n        // Actual SWR hook to load all pages in one fetcher.\n        const swr = useSWRNext(infiniteKey, async (key)=>{\n            // get the revalidate context\n            const forceRevalidateAll = get()._i;\n            const shouldRevalidatePage = get()._r;\n            set({\n                _r: _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.U\n            });\n            // return an array of page data\n            const data = [];\n            const pageSize = resolvePageSize();\n            const [getCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, key);\n            const cacheData = getCache().data;\n            const revalidators = [];\n            let previousPageData = null;\n            for(let i = 0; i < pageSize; ++i){\n                const [pageKey, pageArg] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.s)(getKey(i, parallel ? null : previousPageData));\n                if (!pageKey) {\n                    break;\n                }\n                const [getSWRCache, setSWRCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, pageKey);\n                // Get the cached page data.\n                let pageData = getSWRCache().data;\n                // should fetch (or revalidate) if:\n                // - `revalidateAll` is enabled\n                // - `mutate()` called\n                // - the cache is missing\n                // - it's the first page and it's not the initial render\n                // - `revalidateOnMount` is enabled and it's on mount\n                // - cache for that page has changed\n                const shouldFetchPage = revalidateAll || forceRevalidateAll || (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(pageData) || revalidateFirstPage && !i && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(cacheData) || shouldRevalidateOnMount || cacheData && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(cacheData[i]) && !config.compare(cacheData[i], pageData);\n                if (fn && (typeof shouldRevalidatePage === 'function' ? shouldRevalidatePage(pageData, pageArg) : shouldFetchPage)) {\n                    const revalidate = async ()=>{\n                        const hasPreloadedRequest = pageKey in PRELOAD;\n                        if (!hasPreloadedRequest) {\n                            pageData = await fn(pageArg);\n                        } else {\n                            const req = PRELOAD[pageKey];\n                            // delete the preload cache key before resolving it\n                            // in case there's an error\n                            delete PRELOAD[pageKey];\n                            // get the page data from the preload cache\n                            pageData = await req;\n                        }\n                        setSWRCache({\n                            data: pageData,\n                            _k: pageArg\n                        });\n                        data[i] = pageData;\n                    };\n                    if (parallel) {\n                        revalidators.push(revalidate);\n                    } else {\n                        await revalidate();\n                    }\n                } else {\n                    data[i] = pageData;\n                }\n                if (!parallel) {\n                    previousPageData = pageData;\n                }\n            }\n            // flush all revalidateions in parallel\n            if (parallel) {\n                await Promise.all(revalidators.map((r)=>r()));\n            }\n            // once we executed the data fetching based on the context, clear the context\n            set({\n                _i: _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.U\n            });\n            // return the data\n            return data;\n        }, config);\n        const mutate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(// eslint-disable-next-line func-names\n        function(data, opts) {\n            // When passing as a boolean, it's explicitly used to disable/enable\n            // revalidation.\n            const options = typeof opts === 'boolean' ? {\n                revalidate: opts\n            } : opts || {};\n            // Default to true.\n            const shouldRevalidate = options.revalidate !== false;\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            if (shouldRevalidate) {\n                if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(data)) {\n                    // We only revalidate the pages that are changed\n                    set({\n                        _i: false,\n                        _r: options.revalidate\n                    });\n                } else {\n                    // Calling `mutate()`, we revalidate all pages\n                    set({\n                        _i: true,\n                        _r: options.revalidate\n                    });\n                }\n            }\n            return arguments.length ? swr.mutate(data, {\n                ...options,\n                revalidate: shouldRevalidate\n            }) : swr.mutate();\n        }, // swr.mutate is always the same reference\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache$1\n        ]);\n        // Extend the SWR API\n        const setSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((arg)=>{\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            const [, changeSize] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, infiniteKey);\n            let size;\n            if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.a)(arg)) {\n                size = arg(resolvePageSize());\n            } else if (typeof arg == 'number') {\n                size = arg;\n            }\n            if (typeof size != 'number') return EMPTY_PROMISE;\n            changeSize({\n                _l: size\n            });\n            lastPageSizeRef.current = size;\n            // Calculate the page data after the size change.\n            const data = [];\n            const [getInfiniteCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, infiniteKey);\n            let previousPageData = null;\n            for(let i = 0; i < size; ++i){\n                const [pageKey] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.s)(getKey(i, previousPageData));\n                const [getCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, pageKey);\n                // Get the cached page data.\n                const pageData = pageKey ? getCache().data : _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.U;\n                // Call `mutate` with infinte cache data if we can't get it from the page cache.\n                if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(pageData)) {\n                    return mutate(getInfiniteCache().data);\n                }\n                data.push(pageData);\n                previousPageData = pageData;\n            }\n            return mutate(data);\n        }, // exclude getKey from the dependencies, which isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache$1,\n            mutate,\n            resolvePageSize\n        ]);\n        // Use getter functions to avoid unnecessary re-renders caused by triggering\n        // all the getters of the returned swr object.\n        return {\n            size: resolvePageSize(),\n            setSize,\n            mutate,\n            get data () {\n                return swr.data;\n            },\n            get error () {\n                return swr.error;\n            },\n            get isValidating () {\n                return swr.isValidating;\n            },\n            get isLoading () {\n                return swr.isLoading;\n            }\n        };\n    };\nconst useSWRInfinite = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.withMiddleware)(_index_index_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"], infinite);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/infinite/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/mutation/index.mjs":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/mutation/index.mjs ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSWRMutation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _index_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/index.mjs\");\n/* harmony import */ var _index_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../index/index.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/index/index.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs\");\n\n\n\n\nconst startTransition = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_1__.I ? (cb)=>{\n    cb();\n} : react__WEBPACK_IMPORTED_MODULE_0__.startTransition;\n/**\n * An implementation of state with dependency-tracking.\n * @param initialState - The initial state object.\n */ const useStateWithDeps = (initialState)=>{\n    const [, rerender] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    const unmountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const stateRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(initialState);\n    // If a state property (data, error, or isValidating) is accessed by the render\n    // function, we mark the property as a dependency so if it is updated again\n    // in the future, we trigger a rerender.\n    // This is also known as dependency-tracking.\n    const stateDependenciesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        data: false,\n        error: false,\n        isValidating: false\n    });\n    /**\n   * Updates state and triggers re-render if necessary.\n   * @param payload To change stateRef, pass the values explicitly to setState:\n   * @example\n   * ```js\n   * setState({\n   *   isValidating: false\n   *   data: newData // set data to newData\n   *   error: undefined // set error to undefined\n   * })\n   *\n   * setState({\n   *   isValidating: false\n   *   data: undefined // set data to undefined\n   *   error: err // set error to err\n   * })\n   * ```\n   */ const setState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((payload)=>{\n        let shouldRerender = false;\n        const currentState = stateRef.current;\n        for(const key in payload){\n            if (Object.prototype.hasOwnProperty.call(payload, key)) {\n                const k = key;\n                // If the property has changed, update the state and mark rerender as\n                // needed.\n                if (currentState[k] !== payload[k]) {\n                    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                    currentState[k] = payload[k];\n                    // If the property is accessed by the component, a rerender should be\n                    // triggered.\n                    if (stateDependenciesRef.current[k]) {\n                        shouldRerender = true;\n                    }\n                }\n            }\n        }\n        if (shouldRerender && !unmountedRef.current) {\n            rerender({});\n        }\n    }, []);\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_1__.u)(()=>{\n        unmountedRef.current = false;\n        return ()=>{\n            unmountedRef.current = true;\n        };\n    });\n    return [\n        stateRef,\n        stateDependenciesRef.current,\n        setState\n    ];\n};\n\nconst mutation = ()=>(key, fetcher, config = {})=>{\n        const { mutate } = (0,_index_index_mjs__WEBPACK_IMPORTED_MODULE_2__.useSWRConfig)();\n        const keyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(key);\n        const fetcherRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(fetcher);\n        const configRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(config);\n        // Ditch all mutation results that happened earlier than this timestamp.\n        const ditchMutationsUntilRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n        const [stateRef, stateDependencies, setState] = useStateWithDeps({\n            data: _internal_index_mjs__WEBPACK_IMPORTED_MODULE_1__.U,\n            error: _internal_index_mjs__WEBPACK_IMPORTED_MODULE_1__.U,\n            isMutating: false\n        });\n        const currentState = stateRef.current;\n        const trigger = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (arg, opts)=>{\n            const [serializedKey, resolvedKey] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_1__.s)(keyRef.current);\n            if (!fetcherRef.current) {\n                throw new Error('Can’t trigger the mutation: missing fetcher.');\n            }\n            if (!serializedKey) {\n                throw new Error('Can’t trigger the mutation: missing key.');\n            }\n            // Disable cache population by default.\n            const options = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_1__.m)((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_1__.m)({\n                populateCache: false,\n                throwOnError: true\n            }, configRef.current), opts);\n            // Trigger a mutation, and also track the timestamp. Any mutation that happened\n            // earlier this timestamp should be ignored.\n            const mutationStartedAt = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_1__.o)();\n            ditchMutationsUntilRef.current = mutationStartedAt;\n            setState({\n                isMutating: true\n            });\n            try {\n                const data = await mutate(serializedKey, fetcherRef.current(resolvedKey, {\n                    arg\n                }), // We must throw the error here so we can catch and update the states.\n                (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_1__.m)(options, {\n                    throwOnError: true\n                }));\n                // If it's reset after the mutation, we don't broadcast any state change.\n                if (ditchMutationsUntilRef.current <= mutationStartedAt) {\n                    startTransition(()=>setState({\n                            data,\n                            isMutating: false,\n                            error: undefined\n                        }));\n                    options.onSuccess == null ? void 0 : options.onSuccess.call(options, data, serializedKey, options);\n                }\n                return data;\n            } catch (error) {\n                // If it's reset after the mutation, we don't broadcast any state change\n                // or throw because it's discarded.\n                if (ditchMutationsUntilRef.current <= mutationStartedAt) {\n                    startTransition(()=>setState({\n                            error: error,\n                            isMutating: false\n                        }));\n                    options.onError == null ? undefined : options.onError.call(options, error, serializedKey, options);\n                    if (options.throwOnError) {\n                        throw error;\n                    }\n                }\n            }\n        }, // eslint-disable-next-line react-hooks/exhaustive-deps\n        []);\n        const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n            ditchMutationsUntilRef.current = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_1__.o)();\n            setState({\n                data: _internal_index_mjs__WEBPACK_IMPORTED_MODULE_1__.U,\n                error: _internal_index_mjs__WEBPACK_IMPORTED_MODULE_1__.U,\n                isMutating: false\n            });\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, []);\n        (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_1__.u)(()=>{\n            keyRef.current = key;\n            fetcherRef.current = fetcher;\n            configRef.current = config;\n        });\n        // We don't return `mutate` here as it can be pretty confusing (e.g. people\n        // calling `mutate` but they actually mean `trigger`).\n        // And also, `mutate` relies on the useSWR hook to exist too.\n        return {\n            trigger,\n            reset,\n            get data () {\n                stateDependencies.data = true;\n                return currentState.data;\n            },\n            get error () {\n                stateDependencies.error = true;\n                return currentState.error;\n            },\n            get isMutating () {\n                stateDependencies.isMutating = true;\n                return currentState.isMutating;\n            }\n        };\n    };\n/**\n * A hook to define and manually trigger remote mutations like POST, PUT, DELETE and PATCH use cases.\n *\n * @link https://swr.vercel.app/docs/mutation\n * @example\n * ```jsx\n * import useSWRMutation from 'swr/mutation'\n *\n * const {\n *   data,\n *   error,\n *   trigger,\n *   reset,\n *   isMutating\n * } = useSWRMutation(key, fetcher, options?)\n * ```\n */ const useSWRMutation = (0,_index_index_mjs__WEBPACK_IMPORTED_MODULE_2__.withMiddleware)(_index_index_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"], mutation);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vc3dyQDIuMy40X3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvc3dyL2Rpc3QvbXV0YXRpb24vaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTZEO0FBQ0g7QUFDNEY7O0FBRXRKLHdCQUF3QixrREFBZTtBQUN2QztBQUNBLEVBQUUsRUFBRSxrREFBcUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsK0NBQVEsR0FBRztBQUNwQyx5QkFBeUIsNkNBQU07QUFDL0IscUJBQXFCLDZDQUFNO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLDZDQUFNO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLHVCQUF1QixrREFBVztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QjtBQUN2QjtBQUNBLEtBQUs7QUFDTCxJQUFJLHNEQUF5QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsK0NBQStDO0FBQy9DLGdCQUFnQixTQUFTLEVBQUUsOERBQVk7QUFDdkMsdUJBQXVCLDZDQUFNO0FBQzdCLDJCQUEyQiw2Q0FBTTtBQUNqQywwQkFBMEIsNkNBQU07QUFDaEM7QUFDQSx1Q0FBdUMsNkNBQU07QUFDN0M7QUFDQSxrQkFBa0Isa0RBQVM7QUFDM0IsbUJBQW1CLGtEQUFTO0FBQzVCO0FBQ0EsU0FBUztBQUNUO0FBQ0Esd0JBQXdCLGtEQUFXO0FBQ25DLGlEQUFpRCxzREFBUztBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixzREFBWSxDQUFDLHNEQUFZO0FBQ3JEO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLHNDQUFzQyxzREFBWTtBQUNsRDtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLGdCQUFnQixzREFBWTtBQUM1QjtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxzQkFBc0Isa0RBQVc7QUFDakMsNkNBQTZDLHNEQUFZO0FBQ3pEO0FBQ0Esc0JBQXNCLGtEQUFTO0FBQy9CLHVCQUF1QixrREFBUztBQUNoQztBQUNBLGFBQWE7QUFDYjtBQUNBLFNBQVM7QUFDVCxRQUFRLHNEQUF5QjtBQUNqQztBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsMkJBQTJCLGdFQUFjLENBQUMsd0RBQU07O0FBRVgiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tZW1pbWFsL1B5Y2hhcm1Qcm9qZWN0cy9iZXRiZXQtcGxhdGZvcm0vZnJvbnRlbmQvd2ViL25vZGVfbW9kdWxlcy8ucG5wbS9zd3JAMi4zLjRfcmVhY3RAMTkuMS4xL25vZGVfbW9kdWxlcy9zd3IvZGlzdC9tdXRhdGlvbi9pbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VSZWYsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHVzZVNXUiwgeyB1c2VTV1JDb25maWcgfSBmcm9tICcuLi9pbmRleC9pbmRleC5tanMnO1xuaW1wb3J0IHsgSVNfUkVBQ1RfTEVHQUNZLCB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0LCB3aXRoTWlkZGxld2FyZSwgc2VyaWFsaXplLCBtZXJnZU9iamVjdHMsIGdldFRpbWVzdGFtcCwgVU5ERUZJTkVEIH0gZnJvbSAnLi4vX2ludGVybmFsL2luZGV4Lm1qcyc7XG5cbmNvbnN0IHN0YXJ0VHJhbnNpdGlvbiA9IElTX1JFQUNUX0xFR0FDWSA/IChjYik9PntcbiAgICBjYigpO1xufSA6IFJlYWN0LnN0YXJ0VHJhbnNpdGlvbjtcbi8qKlxuICogQW4gaW1wbGVtZW50YXRpb24gb2Ygc3RhdGUgd2l0aCBkZXBlbmRlbmN5LXRyYWNraW5nLlxuICogQHBhcmFtIGluaXRpYWxTdGF0ZSAtIFRoZSBpbml0aWFsIHN0YXRlIG9iamVjdC5cbiAqLyBjb25zdCB1c2VTdGF0ZVdpdGhEZXBzID0gKGluaXRpYWxTdGF0ZSk9PntcbiAgICBjb25zdCBbLCByZXJlbmRlcl0gPSB1c2VTdGF0ZSh7fSk7XG4gICAgY29uc3QgdW5tb3VudGVkUmVmID0gdXNlUmVmKGZhbHNlKTtcbiAgICBjb25zdCBzdGF0ZVJlZiA9IHVzZVJlZihpbml0aWFsU3RhdGUpO1xuICAgIC8vIElmIGEgc3RhdGUgcHJvcGVydHkgKGRhdGEsIGVycm9yLCBvciBpc1ZhbGlkYXRpbmcpIGlzIGFjY2Vzc2VkIGJ5IHRoZSByZW5kZXJcbiAgICAvLyBmdW5jdGlvbiwgd2UgbWFyayB0aGUgcHJvcGVydHkgYXMgYSBkZXBlbmRlbmN5IHNvIGlmIGl0IGlzIHVwZGF0ZWQgYWdhaW5cbiAgICAvLyBpbiB0aGUgZnV0dXJlLCB3ZSB0cmlnZ2VyIGEgcmVyZW5kZXIuXG4gICAgLy8gVGhpcyBpcyBhbHNvIGtub3duIGFzIGRlcGVuZGVuY3ktdHJhY2tpbmcuXG4gICAgY29uc3Qgc3RhdGVEZXBlbmRlbmNpZXNSZWYgPSB1c2VSZWYoe1xuICAgICAgICBkYXRhOiBmYWxzZSxcbiAgICAgICAgZXJyb3I6IGZhbHNlLFxuICAgICAgICBpc1ZhbGlkYXRpbmc6IGZhbHNlXG4gICAgfSk7XG4gICAgLyoqXG4gICAqIFVwZGF0ZXMgc3RhdGUgYW5kIHRyaWdnZXJzIHJlLXJlbmRlciBpZiBuZWNlc3NhcnkuXG4gICAqIEBwYXJhbSBwYXlsb2FkIFRvIGNoYW5nZSBzdGF0ZVJlZiwgcGFzcyB0aGUgdmFsdWVzIGV4cGxpY2l0bHkgdG8gc2V0U3RhdGU6XG4gICAqIEBleGFtcGxlXG4gICAqIGBgYGpzXG4gICAqIHNldFN0YXRlKHtcbiAgICogICBpc1ZhbGlkYXRpbmc6IGZhbHNlXG4gICAqICAgZGF0YTogbmV3RGF0YSAvLyBzZXQgZGF0YSB0byBuZXdEYXRhXG4gICAqICAgZXJyb3I6IHVuZGVmaW5lZCAvLyBzZXQgZXJyb3IgdG8gdW5kZWZpbmVkXG4gICAqIH0pXG4gICAqXG4gICAqIHNldFN0YXRlKHtcbiAgICogICBpc1ZhbGlkYXRpbmc6IGZhbHNlXG4gICAqICAgZGF0YTogdW5kZWZpbmVkIC8vIHNldCBkYXRhIHRvIHVuZGVmaW5lZFxuICAgKiAgIGVycm9yOiBlcnIgLy8gc2V0IGVycm9yIHRvIGVyclxuICAgKiB9KVxuICAgKiBgYGBcbiAgICovIGNvbnN0IHNldFN0YXRlID0gdXNlQ2FsbGJhY2soKHBheWxvYWQpPT57XG4gICAgICAgIGxldCBzaG91bGRSZXJlbmRlciA9IGZhbHNlO1xuICAgICAgICBjb25zdCBjdXJyZW50U3RhdGUgPSBzdGF0ZVJlZi5jdXJyZW50O1xuICAgICAgICBmb3IoY29uc3Qga2V5IGluIHBheWxvYWQpe1xuICAgICAgICAgICAgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChwYXlsb2FkLCBrZXkpKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgayA9IGtleTtcbiAgICAgICAgICAgICAgICAvLyBJZiB0aGUgcHJvcGVydHkgaGFzIGNoYW5nZWQsIHVwZGF0ZSB0aGUgc3RhdGUgYW5kIG1hcmsgcmVyZW5kZXIgYXNcbiAgICAgICAgICAgICAgICAvLyBuZWVkZWQuXG4gICAgICAgICAgICAgICAgaWYgKGN1cnJlbnRTdGF0ZVtrXSAhPT0gcGF5bG9hZFtrXSkge1xuICAgICAgICAgICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLW5vbi1udWxsLWFzc2VydGlvblxuICAgICAgICAgICAgICAgICAgICBjdXJyZW50U3RhdGVba10gPSBwYXlsb2FkW2tdO1xuICAgICAgICAgICAgICAgICAgICAvLyBJZiB0aGUgcHJvcGVydHkgaXMgYWNjZXNzZWQgYnkgdGhlIGNvbXBvbmVudCwgYSByZXJlbmRlciBzaG91bGQgYmVcbiAgICAgICAgICAgICAgICAgICAgLy8gdHJpZ2dlcmVkLlxuICAgICAgICAgICAgICAgICAgICBpZiAoc3RhdGVEZXBlbmRlbmNpZXNSZWYuY3VycmVudFtrXSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgc2hvdWxkUmVyZW5kZXIgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChzaG91bGRSZXJlbmRlciAmJiAhdW5tb3VudGVkUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgICAgIHJlcmVuZGVyKHt9KTtcbiAgICAgICAgfVxuICAgIH0sIFtdKTtcbiAgICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpPT57XG4gICAgICAgIHVubW91bnRlZFJlZi5jdXJyZW50ID0gZmFsc2U7XG4gICAgICAgIHJldHVybiAoKT0+e1xuICAgICAgICAgICAgdW5tb3VudGVkUmVmLmN1cnJlbnQgPSB0cnVlO1xuICAgICAgICB9O1xuICAgIH0pO1xuICAgIHJldHVybiBbXG4gICAgICAgIHN0YXRlUmVmLFxuICAgICAgICBzdGF0ZURlcGVuZGVuY2llc1JlZi5jdXJyZW50LFxuICAgICAgICBzZXRTdGF0ZVxuICAgIF07XG59O1xuXG5jb25zdCBtdXRhdGlvbiA9ICgpPT4oa2V5LCBmZXRjaGVyLCBjb25maWcgPSB7fSk9PntcbiAgICAgICAgY29uc3QgeyBtdXRhdGUgfSA9IHVzZVNXUkNvbmZpZygpO1xuICAgICAgICBjb25zdCBrZXlSZWYgPSB1c2VSZWYoa2V5KTtcbiAgICAgICAgY29uc3QgZmV0Y2hlclJlZiA9IHVzZVJlZihmZXRjaGVyKTtcbiAgICAgICAgY29uc3QgY29uZmlnUmVmID0gdXNlUmVmKGNvbmZpZyk7XG4gICAgICAgIC8vIERpdGNoIGFsbCBtdXRhdGlvbiByZXN1bHRzIHRoYXQgaGFwcGVuZWQgZWFybGllciB0aGFuIHRoaXMgdGltZXN0YW1wLlxuICAgICAgICBjb25zdCBkaXRjaE11dGF0aW9uc1VudGlsUmVmID0gdXNlUmVmKDApO1xuICAgICAgICBjb25zdCBbc3RhdGVSZWYsIHN0YXRlRGVwZW5kZW5jaWVzLCBzZXRTdGF0ZV0gPSB1c2VTdGF0ZVdpdGhEZXBzKHtcbiAgICAgICAgICAgIGRhdGE6IFVOREVGSU5FRCxcbiAgICAgICAgICAgIGVycm9yOiBVTkRFRklORUQsXG4gICAgICAgICAgICBpc011dGF0aW5nOiBmYWxzZVxuICAgICAgICB9KTtcbiAgICAgICAgY29uc3QgY3VycmVudFN0YXRlID0gc3RhdGVSZWYuY3VycmVudDtcbiAgICAgICAgY29uc3QgdHJpZ2dlciA9IHVzZUNhbGxiYWNrKGFzeW5jIChhcmcsIG9wdHMpPT57XG4gICAgICAgICAgICBjb25zdCBbc2VyaWFsaXplZEtleSwgcmVzb2x2ZWRLZXldID0gc2VyaWFsaXplKGtleVJlZi5jdXJyZW50KTtcbiAgICAgICAgICAgIGlmICghZmV0Y2hlclJlZi5jdXJyZW50KSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdDYW7igJl0IHRyaWdnZXIgdGhlIG11dGF0aW9uOiBtaXNzaW5nIGZldGNoZXIuJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIXNlcmlhbGl6ZWRLZXkpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0NhbuKAmXQgdHJpZ2dlciB0aGUgbXV0YXRpb246IG1pc3Npbmcga2V5LicpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gRGlzYWJsZSBjYWNoZSBwb3B1bGF0aW9uIGJ5IGRlZmF1bHQuXG4gICAgICAgICAgICBjb25zdCBvcHRpb25zID0gbWVyZ2VPYmplY3RzKG1lcmdlT2JqZWN0cyh7XG4gICAgICAgICAgICAgICAgcG9wdWxhdGVDYWNoZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgdGhyb3dPbkVycm9yOiB0cnVlXG4gICAgICAgICAgICB9LCBjb25maWdSZWYuY3VycmVudCksIG9wdHMpO1xuICAgICAgICAgICAgLy8gVHJpZ2dlciBhIG11dGF0aW9uLCBhbmQgYWxzbyB0cmFjayB0aGUgdGltZXN0YW1wLiBBbnkgbXV0YXRpb24gdGhhdCBoYXBwZW5lZFxuICAgICAgICAgICAgLy8gZWFybGllciB0aGlzIHRpbWVzdGFtcCBzaG91bGQgYmUgaWdub3JlZC5cbiAgICAgICAgICAgIGNvbnN0IG11dGF0aW9uU3RhcnRlZEF0ID0gZ2V0VGltZXN0YW1wKCk7XG4gICAgICAgICAgICBkaXRjaE11dGF0aW9uc1VudGlsUmVmLmN1cnJlbnQgPSBtdXRhdGlvblN0YXJ0ZWRBdDtcbiAgICAgICAgICAgIHNldFN0YXRlKHtcbiAgICAgICAgICAgICAgICBpc011dGF0aW5nOiB0cnVlXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IG11dGF0ZShzZXJpYWxpemVkS2V5LCBmZXRjaGVyUmVmLmN1cnJlbnQocmVzb2x2ZWRLZXksIHtcbiAgICAgICAgICAgICAgICAgICAgYXJnXG4gICAgICAgICAgICAgICAgfSksIC8vIFdlIG11c3QgdGhyb3cgdGhlIGVycm9yIGhlcmUgc28gd2UgY2FuIGNhdGNoIGFuZCB1cGRhdGUgdGhlIHN0YXRlcy5cbiAgICAgICAgICAgICAgICBtZXJnZU9iamVjdHMob3B0aW9ucywge1xuICAgICAgICAgICAgICAgICAgICB0aHJvd09uRXJyb3I6IHRydWVcbiAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgLy8gSWYgaXQncyByZXNldCBhZnRlciB0aGUgbXV0YXRpb24sIHdlIGRvbid0IGJyb2FkY2FzdCBhbnkgc3RhdGUgY2hhbmdlLlxuICAgICAgICAgICAgICAgIGlmIChkaXRjaE11dGF0aW9uc1VudGlsUmVmLmN1cnJlbnQgPD0gbXV0YXRpb25TdGFydGVkQXQpIHtcbiAgICAgICAgICAgICAgICAgICAgc3RhcnRUcmFuc2l0aW9uKCgpPT5zZXRTdGF0ZSh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc011dGF0aW5nOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvcjogdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgICAgIG9wdGlvbnMub25TdWNjZXNzID09IG51bGwgPyB2b2lkIDAgOiBvcHRpb25zLm9uU3VjY2Vzcy5jYWxsKG9wdGlvbnMsIGRhdGEsIHNlcmlhbGl6ZWRLZXksIG9wdGlvbnMpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gZGF0YTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgLy8gSWYgaXQncyByZXNldCBhZnRlciB0aGUgbXV0YXRpb24sIHdlIGRvbid0IGJyb2FkY2FzdCBhbnkgc3RhdGUgY2hhbmdlXG4gICAgICAgICAgICAgICAgLy8gb3IgdGhyb3cgYmVjYXVzZSBpdCdzIGRpc2NhcmRlZC5cbiAgICAgICAgICAgICAgICBpZiAoZGl0Y2hNdXRhdGlvbnNVbnRpbFJlZi5jdXJyZW50IDw9IG11dGF0aW9uU3RhcnRlZEF0KSB7XG4gICAgICAgICAgICAgICAgICAgIHN0YXJ0VHJhbnNpdGlvbigoKT0+c2V0U3RhdGUoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yOiBlcnJvcixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc011dGF0aW5nOiBmYWxzZVxuICAgICAgICAgICAgICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgICAgICAgICBvcHRpb25zLm9uRXJyb3IgPT0gbnVsbCA/IHVuZGVmaW5lZCA6IG9wdGlvbnMub25FcnJvci5jYWxsKG9wdGlvbnMsIGVycm9yLCBzZXJpYWxpemVkS2V5LCBvcHRpb25zKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKG9wdGlvbnMudGhyb3dPbkVycm9yKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSwgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xuICAgICAgICBbXSk7XG4gICAgICAgIGNvbnN0IHJlc2V0ID0gdXNlQ2FsbGJhY2soKCk9PntcbiAgICAgICAgICAgIGRpdGNoTXV0YXRpb25zVW50aWxSZWYuY3VycmVudCA9IGdldFRpbWVzdGFtcCgpO1xuICAgICAgICAgICAgc2V0U3RhdGUoe1xuICAgICAgICAgICAgICAgIGRhdGE6IFVOREVGSU5FRCxcbiAgICAgICAgICAgICAgICBlcnJvcjogVU5ERUZJTkVELFxuICAgICAgICAgICAgICAgIGlzTXV0YXRpbmc6IGZhbHNlXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xuICAgICAgICB9LCBbXSk7XG4gICAgICAgIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QoKCk9PntcbiAgICAgICAgICAgIGtleVJlZi5jdXJyZW50ID0ga2V5O1xuICAgICAgICAgICAgZmV0Y2hlclJlZi5jdXJyZW50ID0gZmV0Y2hlcjtcbiAgICAgICAgICAgIGNvbmZpZ1JlZi5jdXJyZW50ID0gY29uZmlnO1xuICAgICAgICB9KTtcbiAgICAgICAgLy8gV2UgZG9uJ3QgcmV0dXJuIGBtdXRhdGVgIGhlcmUgYXMgaXQgY2FuIGJlIHByZXR0eSBjb25mdXNpbmcgKGUuZy4gcGVvcGxlXG4gICAgICAgIC8vIGNhbGxpbmcgYG11dGF0ZWAgYnV0IHRoZXkgYWN0dWFsbHkgbWVhbiBgdHJpZ2dlcmApLlxuICAgICAgICAvLyBBbmQgYWxzbywgYG11dGF0ZWAgcmVsaWVzIG9uIHRoZSB1c2VTV1IgaG9vayB0byBleGlzdCB0b28uXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB0cmlnZ2VyLFxuICAgICAgICAgICAgcmVzZXQsXG4gICAgICAgICAgICBnZXQgZGF0YSAoKSB7XG4gICAgICAgICAgICAgICAgc3RhdGVEZXBlbmRlbmNpZXMuZGF0YSA9IHRydWU7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGN1cnJlbnRTdGF0ZS5kYXRhO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGdldCBlcnJvciAoKSB7XG4gICAgICAgICAgICAgICAgc3RhdGVEZXBlbmRlbmNpZXMuZXJyb3IgPSB0cnVlO1xuICAgICAgICAgICAgICAgIHJldHVybiBjdXJyZW50U3RhdGUuZXJyb3I7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZ2V0IGlzTXV0YXRpbmcgKCkge1xuICAgICAgICAgICAgICAgIHN0YXRlRGVwZW5kZW5jaWVzLmlzTXV0YXRpbmcgPSB0cnVlO1xuICAgICAgICAgICAgICAgIHJldHVybiBjdXJyZW50U3RhdGUuaXNNdXRhdGluZztcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICB9O1xuLyoqXG4gKiBBIGhvb2sgdG8gZGVmaW5lIGFuZCBtYW51YWxseSB0cmlnZ2VyIHJlbW90ZSBtdXRhdGlvbnMgbGlrZSBQT1NULCBQVVQsIERFTEVURSBhbmQgUEFUQ0ggdXNlIGNhc2VzLlxuICpcbiAqIEBsaW5rIGh0dHBzOi8vc3dyLnZlcmNlbC5hcHAvZG9jcy9tdXRhdGlvblxuICogQGV4YW1wbGVcbiAqIGBgYGpzeFxuICogaW1wb3J0IHVzZVNXUk11dGF0aW9uIGZyb20gJ3N3ci9tdXRhdGlvbidcbiAqXG4gKiBjb25zdCB7XG4gKiAgIGRhdGEsXG4gKiAgIGVycm9yLFxuICogICB0cmlnZ2VyLFxuICogICByZXNldCxcbiAqICAgaXNNdXRhdGluZ1xuICogfSA9IHVzZVNXUk11dGF0aW9uKGtleSwgZmV0Y2hlciwgb3B0aW9ucz8pXG4gKiBgYGBcbiAqLyBjb25zdCB1c2VTV1JNdXRhdGlvbiA9IHdpdGhNaWRkbGV3YXJlKHVzZVNXUiwgbXV0YXRpb24pO1xuXG5leHBvcnQgeyB1c2VTV1JNdXRhdGlvbiBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/mutation/index.mjs\n");

/***/ })

};
;