"use client"

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Heart, MessageCircle, Share2, Send, Reply, ChevronDown, ChevronUp } from "lucide-react"
import { useAuth } from "@clerk/nextjs"
// Simple date formatting utility
const formatTimeAgo = (dateString: string): string => {
  const now = new Date()
  const date = new Date(dateString)
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) return `${diffInSeconds}s`
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`
  return `${Math.floor(diffInSeconds / 86400)}d`
}

// Types
interface Activity {
  id: string
  user_id: string
  user_name: string
  user_avatar?: string
  type: string
  content: string
  metadata?: any
  timestamp: string
  likes: number
  comments: number
  shares: number
  is_liked: boolean
}

interface Comment {
  id: string
  activity_id: string
  user_id: string
  user_name: string
  user_avatar?: string
  content: string
  parent_id?: string
  reply_count: number
  like_count: number
  is_liked: boolean
  is_edited: boolean
  created_at: string
  updated_at: string
  replies: Comment[]
}

interface WebSocketMessage {
  type: string
  activity_id?: string
  comment_id?: string
  user_id: string
  action?: string
  likes_count?: number
  shares_count?: number
  comments_count?: number
  is_liked?: boolean
  like_count?: number
  comment?: Comment
}

export function EnhancedActivityFeed() {
  const { userId, getToken } = useAuth()
  const [activities, setActivities] = useState<Activity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [websockets, setWebsockets] = useState<Map<string, WebSocket>>(new Map())
  
  // Comment states
  const [comments, setComments] = useState<Map<string, Comment[]>>(new Map())
  const [commentInputs, setCommentInputs] = useState<Map<string, string>>(new Map())
  const [replyInputs, setReplyInputs] = useState<Map<string, string>>(new Map())
  const [showComments, setShowComments] = useState<Set<string>>(new Set())
  const [loadingComments, setLoadingComments] = useState<Set<string>>(new Set())

  const SOCIAL_API_BASE = process.env.NEXT_PUBLIC_SOCIAL_GAMING_URL || process.env.NEXT_PUBLIC_SOCIAL_API_URL || "http://localhost:8009"

  // Fetch activities
  const fetchActivities = useCallback(async () => {
    try {
      const response = await fetch(`${SOCIAL_API_BASE}/api/v1/feed/public`)
      if (!response.ok) throw new Error('Failed to fetch activities')
      
      const data = await response.json()
      setActivities(data)
      setError(null)
    } catch (err) {
      console.error('Error fetching activities:', err)
      setError('Failed to load activities')
    } finally {
      setLoading(false)
    }
  }, [SOCIAL_API_BASE])

  // Fetch comments for an activity
  const fetchComments = useCallback(async (activityId: string) => {
    setLoadingComments(prev => new Set([...prev, activityId]))
    
    try {
      // Use public comments endpoint that doesn't require authentication
      const response = await fetch(
        `${SOCIAL_API_BASE}/api/v1/feed/${activityId}/comments/public`,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
      
      if (!response.ok) {
        throw new Error(`Failed to fetch comments: ${response.status}`)
      }
      
      const data = await response.json()
      setComments(prev => new Map(prev.set(activityId, data)))
    } catch (err) {
      console.error('Error fetching comments:', err)
      // Set empty comments on error to prevent infinite loading
      setComments(prev => new Map(prev.set(activityId, [])))
    } finally {
      setLoadingComments(prev => {
        const newSet = new Set(prev)
        newSet.delete(activityId)
        return newSet
      })
    }
  }, [SOCIAL_API_BASE])

  // WebSocket connection management
  const connectWebSocket = useCallback((activityId: string) => {
    if (websockets.has(activityId)) return

    try {
      // Convert HTTP URL to WebSocket URL correctly
      const wsUrl = SOCIAL_API_BASE.replace('http://', 'ws://').replace('https://', 'wss://') + `/api/v1/social/ws/activity/${activityId}`
      console.log('Attempting WebSocket connection to:', wsUrl)
      const ws = new WebSocket(wsUrl)

    ws.onopen = () => {
      console.log(`Connected to activity ${activityId} WebSocket`)
      // Send ping to keep connection alive
      const pingInterval = setInterval(() => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.send('ping')
        }
      }, 30000)
      
      ws.addEventListener('close', () => clearInterval(pingInterval))
    }

    ws.onmessage = (event) => {
      // Handle pong messages
      if (event.data === 'pong') {
        return
      }
      
      try {
        const data: WebSocketMessage = JSON.parse(event.data)
        handleWebSocketMessage(data)
      } catch (err) {
        // Only log error if it's not a pong message
        if (event.data !== 'pong') {
          console.error('Error parsing WebSocket message:', err, 'Data:', event.data)
        }
      }
    }

    ws.onerror = (error) => {
      console.warn('WebSocket connection error for activity:', activityId)
      // Don't fail the entire component - WebSocket is optional for basic functionality
    }

    ws.onclose = (event) => {
      console.log(`WebSocket closed for activity ${activityId}. Code: ${event.code}, Reason: ${event.reason}`)
      setWebsockets(prev => {
        const newMap = new Map(prev)
        newMap.delete(activityId)
        return newMap
      })
    }

      setWebsockets(prev => new Map(prev.set(activityId, ws)))
    } catch (error) {
      console.warn('Failed to create WebSocket connection for activity:', activityId, error)
      // WebSocket is optional - continue without real-time updates
    }
  }, [websockets, SOCIAL_API_BASE])

  // Handle WebSocket real-time updates
  const handleWebSocketMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'like_update':
        setActivities(prev => prev.map(activity => 
          activity.id === message.activity_id ? {
            ...activity,
            likes: message.likes_count || activity.likes,
            is_liked: message.user_id === userId ? message.is_liked! : activity.is_liked
          } : activity
        ))
        break

      case 'comment_added':
        if (message.comment) {
          setComments(prev => {
            const activityComments = prev.get(message.activity_id!) || []
            return new Map(prev.set(message.activity_id!, [...activityComments, message.comment!]))
          })
        }
        setActivities(prev => prev.map(activity => 
          activity.id === message.activity_id ? {
            ...activity,
            comments: message.comments_count || activity.comments
          } : activity
        ))
        break

      case 'share_update':
        setActivities(prev => prev.map(activity => 
          activity.id === message.activity_id ? {
            ...activity,
            shares: message.shares_count || activity.shares
          } : activity
        ))
        break

      case 'comment_like_update':
        setComments(prev => {
          const activityComments = prev.get(message.activity_id!) || []
          const updatedComments = updateCommentLikes(activityComments, message.comment_id!, message.like_count!, message.is_liked!)
          return new Map(prev.set(message.activity_id!, updatedComments))
        })
        break
    }
  }, [userId])

  // Recursive function to update comment likes
  const updateCommentLikes = (comments: Comment[], commentId: string, likeCount: number, isLiked: boolean): Comment[] => {
    return comments.map(comment => {
      if (comment.id === commentId) {
        return { ...comment, like_count: likeCount, is_liked: isLiked }
      }
      if (comment.replies.length > 0) {
        return { ...comment, replies: updateCommentLikes(comment.replies, commentId, likeCount, isLiked) }
      }
      return comment
    })
  }

  // Recursive function to add reply to comment
  const addReplyToComment = (comments: Comment[], parentId: string, newReply: Comment): Comment[] => {
    return comments.map(comment => {
      if (comment.id === parentId) {
        return { ...comment, replies: [...comment.replies, newReply] }
      }
      if (comment.replies.length > 0) {
        return { ...comment, replies: addReplyToComment(comment.replies, parentId, newReply) }
      }
      return comment
    })
  }

  // Recursive function to remove comment
  const removeComment = (comments: Comment[], commentId: string): Comment[] => {
    return comments.filter(c => c.id !== commentId).map(comment => {
      if (comment.replies.length > 0) {
        return { ...comment, replies: removeComment(comment.replies, commentId) }
      }
      return comment
    })
  }

  // Like/unlike activity
  const handleLike = async (activityId: string) => {
    if (!userId) return

    try {
      const token = await getToken()
      await fetch(`${SOCIAL_API_BASE}/api/v1/social/${activityId}/like`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reaction_type: 'like' })
      })
    } catch (err) {
      console.error('Error liking activity:', err)
    }
  }

  // Add comment
  const handleComment = async (activityId: string, content: string, parentId?: string) => {
    if (!content.trim()) return

    // Optimistic update - add comment immediately to UI
    const tempComment: Comment = {
      id: `temp-${Date.now()}`,
      activity_id: activityId,
      user_id: userId || 'guest_user',
      user_name: userId ? 'You' : 'Guest',
      user_avatar: undefined,
      content: content.trim(),
      parent_id: parentId || null,
      reply_count: 0,
      like_count: 0,
      is_liked: false,
      is_edited: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      replies: []
    }

    // Add comment to state immediately (optimistic update)
    if (parentId) {
      // Add as reply
      setComments(prev => {
        const newMap = new Map(prev)
        const activityComments = newMap.get(activityId) || []
        const updatedComments = addReplyToComment(activityComments, parentId, tempComment)
        newMap.set(activityId, updatedComments)
        return newMap
      })
    } else {
      // Add as top-level comment
      setComments(prev => {
        const newMap = new Map(prev)
        const activityComments = newMap.get(activityId) || []
        newMap.set(activityId, [...activityComments, tempComment])
        return newMap
      })
    }

    try {
      let response
      if (userId) {
        // Authenticated comment
        const token = await getToken()
        response = await fetch(`${SOCIAL_API_BASE}/api/v1/social/${activityId}/comment`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ content: content.trim(), parent_id: parentId })
        })
      } else {
        // Public comment (no authentication required)
        response = await fetch(`${SOCIAL_API_BASE}/api/v1/social/${activityId}/comment/public`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ content: content.trim(), parent_id: parentId })
        })
      }

      if (!response.ok) {
        throw new Error('Failed to post comment')
      }

      // Clear input
      if (parentId) {
        setReplyInputs(prev => {
          const newMap = new Map(prev)
          newMap.delete(parentId)
          return newMap
        })
      } else {
        setCommentInputs(prev => {
          const newMap = new Map(prev)
          newMap.delete(activityId)
          return newMap
        })
      }

      // No need to refresh - optimistic update already shows the comment
    } catch (err) {
      console.error('Error adding comment:', err)
      
      // Remove the optimistic comment on error
      if (parentId) {
        setComments(prev => {
          const newMap = new Map(prev)
          const activityComments = newMap.get(activityId) || []
          const updatedComments = removeComment(activityComments, tempComment.id)
          newMap.set(activityId, updatedComments)
          return newMap
        })
      } else {
        setComments(prev => {
          const newMap = new Map(prev)
          const activityComments = newMap.get(activityId) || []
          newMap.set(activityId, activityComments.filter(c => c.id !== tempComment.id))
          return newMap
        })
      }
    }
  }

  // Like/unlike comment
  const handleCommentLike = async (commentId: string) => {
    if (!userId) return

    try {
      const token = await getToken()
      await fetch(`${SOCIAL_API_BASE}/api/v1/social/comments/${commentId}/like`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })
    } catch (err) {
      console.error('Error liking comment:', err)
    }
  }

  // Share activity
  const handleShare = async (activityId: string) => {
    if (!userId) return

    try {
      const token = await getToken()
      await fetch(`${SOCIAL_API_BASE}/api/v1/social/${activityId}/share`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ shared_to: 'timeline' })
      })
    } catch (err) {
      console.error('Error sharing activity:', err)
    }
  }

  // Toggle comments visibility
  const toggleComments = (activityId: string) => {
    if (showComments.has(activityId)) {
      setShowComments(prev => {
        const newSet = new Set(prev)
        newSet.delete(activityId)
        return newSet
      })
      
      // Close WebSocket connection
      const ws = websockets.get(activityId)
      if (ws) {
        ws.close()
      }
    } else {
      setShowComments(prev => new Set([...prev, activityId]))
      fetchComments(activityId)
      
      // Connect WebSocket for real-time updates (works for all users)
      connectWebSocket(activityId)
    }
  }

  // Render comment recursively
  const renderComment = (comment: Comment, depth: number = 0) => (
    <div key={comment.id} className={`${depth > 0 ? 'ml-6 border-l-2 border-muted pl-4' : ''}`}>
      <div className="flex space-x-3">
        <Avatar className="h-8 w-8">
          <AvatarImage src={comment.user_avatar} />
          <AvatarFallback>{comment.user_name.charAt(0).toUpperCase()}</AvatarFallback>
        </Avatar>
        
        <div className="flex-1">
          <div className="bg-muted rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-1">
              <span className="font-medium text-sm">{comment.user_name}</span>
              <span className="text-xs text-muted-foreground">
                {formatTimeAgo(comment.created_at)} ago
              </span>
              {comment.is_edited && (
                <span className="text-xs text-muted-foreground">(edited)</span>
              )}
            </div>
            <p className="text-sm">{comment.content}</p>
          </div>
          
          <div className="flex items-center space-x-4 mt-2">
            <button
              onClick={() => handleCommentLike(comment.id)}
              className={`flex items-center space-x-1 text-xs ${
                comment.is_liked ? 'text-red-500' : 'text-muted-foreground hover:text-red-500'
              }`}
              disabled={!userId}
            >
              <Heart className={`h-4 w-4 ${comment.is_liked ? 'fill-current' : ''}`} />
              <span>{comment.like_count}</span>
            </button>
            
            <button
              onClick={() => {
                setReplyInputs(prev => new Map(prev.set(comment.id, prev.get(comment.id) || '')))
              }}
              className="flex items-center space-x-1 text-xs text-muted-foreground hover:text-primary"
            >
              <Reply className="h-4 w-4" />
              <span>Reply</span>
            </button>
          </div>

          {/* Reply input */}
          {replyInputs.has(comment.id) && (
            <div className="mt-3 flex space-x-2">
              <Textarea
                placeholder="Write a reply..."
                value={replyInputs.get(comment.id) || ''}
                onChange={(e) => setReplyInputs(prev => new Map(prev.set(comment.id, e.target.value)))}
                className="min-h-[60px] text-sm"
              />
              <Button
                size="sm"
                onClick={() => handleComment(comment.activity_id, replyInputs.get(comment.id) || '', comment.id)}
                disabled={!replyInputs.get(comment.id)?.trim()}
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          )}

          {/* Nested replies */}
          {comment.replies.length > 0 && (
            <div className="mt-3 space-y-3">
              {comment.replies.map(reply => renderComment(reply, depth + 1))}
            </div>
          )}
        </div>
      </div>
    </div>
  )

  useEffect(() => {
    fetchActivities()
  }, [fetchActivities])

  // Cleanup WebSocket connections
  useEffect(() => {
    return () => {
      websockets.forEach(ws => ws.close())
    }
  }, [websockets])

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-muted rounded w-3/4 mb-4"></div>
              <div className="h-20 bg-muted rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">{error}</p>
          <Button onClick={fetchActivities} className="mt-4">Retry</Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {activities.map((activity) => (
        <Card key={activity.id}>
          <CardHeader className="pb-3">
            <div className="flex items-start space-x-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={activity.user_avatar} />
                <AvatarFallback>{activity.user_name.charAt(0).toUpperCase()}</AvatarFallback>
              </Avatar>
              
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className="font-semibold">{activity.user_name}</span>
                  <Badge variant="secondary" className="text-xs">{activity.type}</Badge>
                  <span className="text-sm text-muted-foreground">
                    {formatTimeAgo(activity.timestamp)} ago
                  </span>
                </div>
                <p className="text-sm mt-2">{activity.content}</p>
              </div>
            </div>
          </CardHeader>

          <CardContent>
            {/* Activity metadata */}
            {activity.metadata && Object.keys(activity.metadata).length > 0 && (
              <div className="mb-4 p-3 bg-muted rounded-lg">
                <pre className="text-xs text-muted-foreground">
                  {JSON.stringify(activity.metadata, null, 2)}
                </pre>
              </div>
            )}

            {/* Social actions */}
            <div className="flex items-center space-x-6 py-2 border-t border-b">
              <button
                onClick={() => handleLike(activity.id)}
                className={`flex items-center space-x-2 ${
                  activity.is_liked ? 'text-red-500' : 'text-muted-foreground hover:text-red-500'
                }`}
                disabled={!userId}
              >
                <Heart className={`h-5 w-5 ${activity.is_liked ? 'fill-current' : ''}`} />
                <span>{activity.likes}</span>
              </button>

              <button
                onClick={() => toggleComments(activity.id)}
                className="flex items-center space-x-2 text-muted-foreground hover:text-primary"
              >
                <MessageCircle className="h-5 w-5" />
                <span>{activity.comments}</span>
                {showComments.has(activity.id) ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </button>

              <button
                onClick={() => handleShare(activity.id)}
                className="flex items-center space-x-2 text-muted-foreground hover:text-primary"
                disabled={!userId}
              >
                <Share2 className="h-5 w-5" />
                <span>{activity.shares}</span>
              </button>
            </div>

            {/* Comments section */}
            {showComments.has(activity.id) && (
              <div className="mt-4 space-y-4">
                {/* Comment input - now available to all users */}
                <div className="flex space-x-3">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>{userId ? "Me" : "G"}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1 flex space-x-2">
                    <Textarea
                      placeholder={userId ? "Write a comment..." : "Write a comment as guest..."}
                      value={commentInputs.get(activity.id) || ''}
                      onChange={(e) => setCommentInputs(prev => new Map(prev.set(activity.id, e.target.value)))}
                      className="min-h-[60px]"
                    />
                    <Button
                      onClick={() => handleComment(activity.id, commentInputs.get(activity.id) || '')}
                      disabled={!commentInputs.get(activity.id)?.trim()}
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Comments list */}
                {loadingComments.has(activity.id) ? (
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {(comments.get(activity.id) || []).map(comment => renderComment(comment))}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}