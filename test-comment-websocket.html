<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comment WebSocket Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 8px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .websocket-log { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 4px; max-height: 200px; overflow-y: scroll; }
        button { margin: 5px; padding: 8px 16px; cursor: pointer; }
        input[type="text"] { width: 300px; padding: 8px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Comment + WebSocket Test</h1>
    
    <div class="section">
        <h2>1. WebSocket Connection Status</h2>
        <button onclick="connectWebSocket()">Connect WebSocket</button>
        <button onclick="disconnectWebSocket()">Disconnect</button>
        <div id="wsStatus">Not connected</div>
        <div class="websocket-log" id="wsLog">WebSocket messages will appear here...</div>
    </div>
    
    <div class="section">
        <h2>2. Add Comment</h2>
        <input type="text" id="commentInput" placeholder="Enter your comment..." />
        <button onclick="addComment()">Add Comment</button>
        <div id="commentResult"></div>
    </div>
    
    <div class="section">
        <h2>3. Load Comments</h2>
        <button onclick="loadComments()">Load Comments</button>
        <div id="commentsResult"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8009';
        const ACTIVITY_ID = '95ab0bfd-a2c2-4150-b0ec-d64099e3f287';
        let ws = null;
        
        function connectWebSocket() {
            if (ws) {
                ws.close();
            }
            
            const wsUrl = `ws://localhost:8009/api/v1/social/ws/activity/${ACTIVITY_ID}`;
            ws = new WebSocket(wsUrl);
            
            ws.onopen = () => {
                document.getElementById('wsStatus').innerHTML = '<span style="color: green;">✅ Connected</span>';
                logMessage('WebSocket connected');
                
                // Send ping to keep alive
                setInterval(() => {
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        ws.send('ping');
                    }
                }, 30000);
            };
            
            ws.onmessage = (event) => {
                logMessage(`Received: ${event.data}`);
                try {
                    const data = JSON.parse(event.data);
                    if (data.type === 'comment_added') {
                        logMessage(`🆕 New comment from ${data.comment.user_name}: ${data.comment.content}`);
                    }
                } catch (e) {
                    // Handle non-JSON messages like pong
                }
            };
            
            ws.onerror = (error) => {
                document.getElementById('wsStatus').innerHTML = '<span style="color: red;">❌ Error</span>';
                logMessage(`WebSocket error: ${error}`);
            };
            
            ws.onclose = () => {
                document.getElementById('wsStatus').innerHTML = '<span style="color: orange;">⚠️ Disconnected</span>';
                logMessage('WebSocket disconnected');
            };
        }
        
        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }
        
        function logMessage(message) {
            const log = document.getElementById('wsLog');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }
        
        async function addComment() {
            const input = document.getElementById('commentInput');
            const comment = input.value.trim();
            if (!comment) return;
            
            try {
                const response = await fetch(`${API_BASE}/api/v1/social/${ACTIVITY_ID}/comment/public`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        content: comment,
                        parent_id: null
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    document.getElementById('commentResult').innerHTML = 
                        `<div class="success">✅ Comment added! ID: ${result.comment_id}</div>`;
                    input.value = '';
                    logMessage(`📝 Comment added: "${comment}"`);
                } else {
                    throw new Error(result.detail);
                }
            } catch (error) {
                document.getElementById('commentResult').innerHTML = 
                    `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        async function loadComments() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/feed/${ACTIVITY_ID}/comments/public`);
                const comments = await response.json();
                
                if (response.ok) {
                    let html = `<div class="success"><h3>✅ Loaded ${comments.length} comments</h3>`;
                    comments.forEach(comment => {
                        html += `<div style="margin: 10px 0; padding: 10px; background: white; border-radius: 4px;">
                            <strong>${comment.user_name}:</strong> ${comment.content}<br>
                            <small>Likes: ${comment.like_count} | Created: ${new Date(comment.created_at).toLocaleString()}</small>
                            ${comment.replies.length > 0 ? `<div style="margin-left: 20px; margin-top: 5px;">
                                ${comment.replies.map(reply => 
                                    `<div><strong>${reply.user_name}:</strong> ${reply.content}</div>`
                                ).join('')}
                            </div>` : ''}
                        </div>`;
                    });
                    html += '</div>';
                    document.getElementById('commentsResult').innerHTML = html;
                } else {
                    throw new Error('Failed to load comments');
                }
            } catch (error) {
                document.getElementById('commentsResult').innerHTML = 
                    `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        // Auto-connect when page loads
        window.onload = () => {
            connectWebSocket();
            loadComments();
        };
    </script>
</body>
</html>