"""
Clerk Authentication for Social Gaming Service
==============================================
Simple Clerk token validation for development.
"""

import logging
from typing import Optional
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import jwt

logger = logging.getLogger(__name__)

# HTTP Bearer token security scheme
security = HTTPBearer(auto_error=False)

async def get_current_user_from_clerk(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[str]:
    """
    Get current user ID from Clerk token.
    Returns None if no token or invalid token (for public endpoints).
    """
    if not credentials:
        return None
    
    try:
        # For development, we'll trust the token and extract the user ID
        # In production, you'd validate the token with Clerk's public key
        token = credentials.credentials
        
        # Decode without verification for development
        # WARNING: This is NOT secure for production!
        payload = jwt.decode(token, options={"verify_signature": False})
        
        # Clerk tokens typically have 'sub' field with user ID
        user_id = payload.get('sub') or payload.get('user_id')
        
        if user_id:
            logger.info(f"Authenticated user: {user_id}")
            return user_id
        
        return None
        
    except Exception as e:
        logger.warning(f"Token validation failed: {e}")
        return None

async def require_user_from_clerk(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """
    Get current user ID from Clerk token (required).
    Raises 401 if no valid token.
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user_id = await get_current_user_from_clerk(credentials)
    
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user_id