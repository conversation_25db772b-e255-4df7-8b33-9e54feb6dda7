"""
Enhanced Activity Feed API Endpoints with Real-time Social Features
==================================================================
Complete social functionality including likes, multilevel comments, and shares.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID, uuid4

from fastapi import APIRouter, Depends, HTTPException, status, Query, WebSocket, WebSocketDisconnect
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
import json

# Shared imports
import sys
sys.path.append('/app/shared')
from shared.core.database.connection import get_database_read, get_database_write

# Use local Clerk auth instead of shared auth service
from app.auth.clerk_auth import get_current_user_from_clerk, require_user_from_clerk

logger = logging.getLogger(__name__)
router = APIRouter()

# ====================================================================
# WEBSOCKET CONNECTION MANAGER
# ====================================================================

class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.activity_subscribers: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, activity_id: str = None):
        await websocket.accept()
        self.active_connections.append(websocket)
        if activity_id:
            if activity_id not in self.activity_subscribers:
                self.activity_subscribers[activity_id] = []
            self.activity_subscribers[activity_id].append(websocket)

    def disconnect(self, websocket: WebSocket, activity_id: str = None):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if activity_id and activity_id in self.activity_subscribers:
            if websocket in self.activity_subscribers[activity_id]:
                self.activity_subscribers[activity_id].remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast_to_activity(self, message: dict, activity_id: str):
        if activity_id in self.activity_subscribers:
            for connection in self.activity_subscribers[activity_id]:
                try:
                    await connection.send_json(message)
                except:
                    pass

manager = ConnectionManager()

# ====================================================================
# PYDANTIC MODELS
# ====================================================================

class CommentResponse(BaseModel):
    id: str
    activity_id: str
    user_id: str
    user_name: str
    user_avatar: Optional[str]
    content: str
    parent_id: Optional[str]
    reply_count: int = 0
    like_count: int = 0
    is_liked: bool = False
    is_edited: bool = False
    created_at: datetime
    updated_at: datetime
    replies: List['CommentResponse'] = []

class CommentCreate(BaseModel):
    content: str = Field(..., min_length=1, max_length=1000)
    parent_id: Optional[str] = None

class ReactionCreate(BaseModel):
    reaction_type: str = Field(default="like", pattern=r"^(like|love|wow|laugh|sad|angry)$")

class ShareCreate(BaseModel):
    shared_to: str = Field(..., pattern=r"^(timeline|direct_message|external)$")
    share_message: Optional[str] = Field(None, max_length=300)

# Forward reference for nested comments
CommentResponse.model_rebuild()

# ====================================================================
# WEBSOCKET ENDPOINTS
# ====================================================================

@router.websocket("/ws/activity/{activity_id}")
async def websocket_activity_endpoint(websocket: WebSocket, activity_id: str):
    await manager.connect(websocket, activity_id)
    try:
        while True:
            data = await websocket.receive_text()
            # Keep connection alive - client can send ping messages
            if data == "ping":
                await websocket.send_text("pong")
    except WebSocketDisconnect:
        manager.disconnect(websocket, activity_id)
    except Exception as e:
        logger.error(f"WebSocket error for activity {activity_id}: {e}")
        manager.disconnect(websocket, activity_id)

# ====================================================================
# ENHANCED SOCIAL ENDPOINTS
# ====================================================================

@router.post("/{activity_id}/like")
async def like_activity(
    activity_id: str,
    reaction_data: ReactionCreate = ReactionCreate(),
    user_id: str = Depends(require_user_from_clerk),
    db: AsyncSession = Depends(get_database_write)
):
    """Like or react to an activity with real-time updates."""
    
    try:
        # Check if user already reacted
        check_query = text("""
            SELECT id FROM activity_reactions 
            WHERE activity_id = :activity_id AND user_id = :user_id
        """)
        existing = await db.execute(check_query, {"activity_id": activity_id, "user_id": user_id})
        existing_reaction = existing.fetchone()
        
        is_new_reaction = not existing_reaction
        
        if existing_reaction:
            # Update existing reaction or remove if same type
            if reaction_data.reaction_type == "like":
                # Toggle like - remove if already liked
                delete_query = text("DELETE FROM activity_reactions WHERE id = :id")
                await db.execute(delete_query, {"id": existing_reaction.id})
                action = "unliked"
                is_liked = False
            else:
                # Update to different reaction type
                update_query = text("""
                    UPDATE activity_reactions 
                    SET reaction_type = :reaction_type 
                    WHERE id = :id
                """)
                await db.execute(update_query, {
                    "reaction_type": reaction_data.reaction_type,
                    "id": existing_reaction.id
                })
                action = reaction_data.reaction_type
                is_liked = True
        else:
            # Create new reaction
            insert_query = text("""
                INSERT INTO activity_reactions (activity_id, user_id, reaction_type)
                VALUES (:activity_id, :user_id, :reaction_type)
            """)
            await db.execute(insert_query, {
                "activity_id": activity_id,
                "user_id": user_id,
                "reaction_type": reaction_data.reaction_type
            })
            action = reaction_data.reaction_type
            is_liked = True
        
        # Update activity likes count
        count_query = text("""
            UPDATE user_activities 
            SET likes_count = (
                SELECT COUNT(*) FROM activity_reactions 
                WHERE activity_id = :activity_id AND reaction_type = 'like'
            )
            WHERE id = :activity_id
            RETURNING likes_count
        """)
        result = await db.execute(count_query, {"activity_id": activity_id})
        likes_count = result.fetchone().likes_count
        await db.commit()
        
        # Broadcast real-time update
        await manager.broadcast_to_activity({
            "type": "like_update",
            "activity_id": activity_id,
            "user_id": user_id,
            "action": action,
            "likes_count": likes_count,
            "is_liked": is_liked
        }, activity_id)
        
        return {
            "message": f"Activity {action} successfully", 
            "reaction_type": reaction_data.reaction_type,
            "is_liked": is_liked,
            "likes_count": likes_count
        }
        
    except Exception as e:
        logger.error(f"Error reacting to activity: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to react to activity")

@router.post("/{activity_id}/comment")
async def comment_on_activity(
    activity_id: str,
    comment_data: CommentCreate,
    user_id: str = Depends(require_user_from_clerk),
    db: AsyncSession = Depends(get_database_write)
):
    """Comment on an activity with real-time updates."""
    
    try:
        # Insert comment
        insert_query = text("""
            INSERT INTO activity_comments (activity_id, user_id, content, parent_id)
            VALUES (:activity_id, :user_id, :content, :parent_id)
            RETURNING id, created_at
        """)
        result = await db.execute(insert_query, {
            "activity_id": activity_id,
            "user_id": user_id,
            "content": comment_data.content,
            "parent_id": comment_data.parent_id
        })
        new_comment = result.fetchone()
        
        # Update activity comments count
        count_query = text("""
            UPDATE user_activities 
            SET comments_count = (
                SELECT COUNT(*) FROM activity_comments 
                WHERE activity_id = :activity_id AND is_deleted = FALSE
            )
            WHERE id = :activity_id
            RETURNING comments_count
        """)
        count_result = await db.execute(count_query, {"activity_id": activity_id})
        comments_count = count_result.fetchone().comments_count
        await db.commit()
        
        # Get user info for real-time update
        user_query = text("""
            SELECT COALESCE(sp.display_name, :user_id) as user_name,
                   sp.avatar_url as user_avatar
            FROM social_user_profiles sp 
            WHERE sp.user_id = :user_id
        """)
        user_result = await db.execute(user_query, {"user_id": user_id})
        user_info = user_result.fetchone()
        user_name = user_info.user_name if user_info else user_id
        user_avatar = user_info.user_avatar if user_info else None
        
        # Broadcast real-time update
        await manager.broadcast_to_activity({
            "type": "comment_added",
            "activity_id": activity_id,
            "comment": {
                "id": str(new_comment.id),
                "user_id": user_id,
                "user_name": user_name,
                "user_avatar": user_avatar,
                "content": comment_data.content,
                "parent_id": comment_data.parent_id,
                "created_at": new_comment.created_at.isoformat(),
                "like_count": 0,
                "is_liked": False
            },
            "comments_count": comments_count
        }, activity_id)
        
        return {
            "message": "Comment added successfully",
            "comment_id": str(new_comment.id),
            "created_at": new_comment.created_at.isoformat(),
            "comments_count": comments_count
        }
        
    except Exception as e:
        logger.error(f"Error commenting on activity: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to add comment")

@router.post("/{activity_id}/share")
async def share_activity(
    activity_id: str,
    share_data: ShareCreate,
    user_id: str = Depends(require_user_from_clerk),
    db: AsyncSession = Depends(get_database_write)
):
    """Share an activity with real-time updates."""
    
    try:
        # Insert share record
        insert_query = text("""
            INSERT INTO activity_shares (activity_id, user_id, shared_to, share_message)
            VALUES (:activity_id, :user_id, :shared_to, :share_message)
            RETURNING id, created_at
        """)
        result = await db.execute(insert_query, {
            "activity_id": activity_id,
            "user_id": user_id,
            "shared_to": share_data.shared_to,
            "share_message": share_data.share_message
        })
        new_share = result.fetchone()
        
        # Update activity shares count
        count_query = text("""
            UPDATE user_activities 
            SET shares_count = (
                SELECT COUNT(*) FROM activity_shares WHERE activity_id = :activity_id
            )
            WHERE id = :activity_id
            RETURNING shares_count
        """)
        count_result = await db.execute(count_query, {"activity_id": activity_id})
        shares_count = count_result.fetchone().shares_count
        await db.commit()
        
        # Broadcast real-time update
        await manager.broadcast_to_activity({
            "type": "share_update",
            "activity_id": activity_id,
            "user_id": user_id,
            "shares_count": shares_count,
            "shared_to": share_data.shared_to
        }, activity_id)
        
        return {
            "message": "Activity shared successfully",
            "share_id": str(new_share.id),
            "created_at": new_share.created_at.isoformat(),
            "shares_count": shares_count
        }
        
    except Exception as e:
        logger.error(f"Error sharing activity: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to share activity")

@router.get("/{activity_id}/comments")
async def get_activity_comments(
    activity_id: str,
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    user_id: Optional[str] = Depends(get_current_user_from_clerk),
    db: AsyncSession = Depends(get_database_read)
):
    """Get comments for an activity with nested structure."""
    
    try:
        offset = (page - 1) * limit
        
        # Get all comments for the activity with hierarchical structure
        query = text("""
            WITH RECURSIVE comment_tree AS (
                -- Base case: top-level comments
                SELECT 
                    c.id,
                    c.activity_id,
                    c.user_id,
                    COALESCE(sp.display_name, c.user_id) as user_name,
                    sp.avatar_url as user_avatar,
                    c.content,
                    c.parent_id,
                    c.is_edited,
                    c.created_at,
                    c.updated_at,
                    0 as depth,
                    ARRAY[c.created_at] as sort_path,
                    (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = c.id) as like_count,
                    (CASE 
                        WHEN :user_id IS NOT NULL THEN 
                            EXISTS(SELECT 1 FROM comment_reactions WHERE comment_id = c.id AND user_id = :user_id)
                        ELSE FALSE 
                    END) as is_liked
                FROM activity_comments c
                LEFT JOIN social_user_profiles sp ON c.user_id = sp.user_id
                WHERE c.activity_id = :activity_id 
                    AND c.parent_id IS NULL 
                    AND c.is_deleted = FALSE
                
                UNION ALL
                
                -- Recursive case: replies to comments
                SELECT 
                    c.id,
                    c.activity_id,
                    c.user_id,
                    COALESCE(sp.display_name, c.user_id) as user_name,
                    sp.avatar_url as user_avatar,
                    c.content,
                    c.parent_id,
                    c.is_edited,
                    c.created_at,
                    c.updated_at,
                    ct.depth + 1,
                    ct.sort_path || c.created_at,
                    (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = c.id) as like_count,
                    (CASE 
                        WHEN :user_id IS NOT NULL THEN 
                            EXISTS(SELECT 1 FROM comment_reactions WHERE comment_id = c.id AND user_id = :user_id)
                        ELSE FALSE 
                    END) as is_liked
                FROM activity_comments c
                LEFT JOIN social_user_profiles sp ON c.user_id = sp.user_id
                JOIN comment_tree ct ON c.parent_id = ct.id
                WHERE c.is_deleted = FALSE AND ct.depth < 5
            )
            SELECT * FROM comment_tree
            ORDER BY sort_path
            LIMIT :limit OFFSET :offset
        """)
        
        result = await db.execute(query, {
            "activity_id": activity_id, 
            "user_id": user_id,
            "limit": limit, 
            "offset": offset
        })
        rows = result.fetchall()
        
        # Build nested comment structure
        comments_dict = {}
        top_level_comments = []
        
        for row in rows:
            comment = CommentResponse(
                id=str(row.id),
                activity_id=str(row.activity_id),
                user_id=row.user_id,
                user_name=row.user_name or row.user_id,
                user_avatar=row.user_avatar,
                content=row.content,
                parent_id=str(row.parent_id) if row.parent_id else None,
                like_count=row.like_count,
                is_liked=row.is_liked,
                is_edited=row.is_edited,
                created_at=row.created_at,
                updated_at=row.updated_at,
                replies=[]
            )
            
            comments_dict[str(row.id)] = comment
            
            if row.parent_id is None:
                top_level_comments.append(comment)
            else:
                parent_comment = comments_dict.get(str(row.parent_id))
                if parent_comment:
                    parent_comment.replies.append(comment)
        
        return top_level_comments
        
    except Exception as e:
        logger.error(f"Error fetching comments: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch comments")

@router.post("/comments/{comment_id}/like")
async def like_comment(
    comment_id: str,
    user_id: str = Depends(require_user_from_clerk),
    db: AsyncSession = Depends(get_database_write)
):
    """Like a comment with real-time updates."""
    
    try:
        # Get activity_id for broadcasting
        activity_query = text("SELECT activity_id FROM activity_comments WHERE id = :comment_id")
        activity_result = await db.execute(activity_query, {"comment_id": comment_id})
        activity_row = activity_result.fetchone()
        
        if not activity_row:
            raise HTTPException(status_code=404, detail="Comment not found")
        
        activity_id = str(activity_row.activity_id)
        
        # Check if user already liked this comment
        check_query = text("""
            SELECT id FROM comment_reactions 
            WHERE comment_id = :comment_id AND user_id = :user_id
        """)
        existing = await db.execute(check_query, {"comment_id": comment_id, "user_id": user_id})
        existing_reaction = existing.fetchone()
        
        if existing_reaction:
            # Remove like
            delete_query = text("DELETE FROM comment_reactions WHERE id = :id")
            await db.execute(delete_query, {"id": existing_reaction.id})
            message = "Comment unliked"
            is_liked = False
        else:
            # Add like
            insert_query = text("""
                INSERT INTO comment_reactions (comment_id, user_id, reaction_type)
                VALUES (:comment_id, :user_id, 'like')
            """)
            await db.execute(insert_query, {
                "comment_id": comment_id,
                "user_id": user_id
            })
            message = "Comment liked"
            is_liked = True
        
        # Get updated like count
        count_query = text("SELECT COUNT(*) as count FROM comment_reactions WHERE comment_id = :comment_id")
        count_result = await db.execute(count_query, {"comment_id": comment_id})
        like_count = count_result.fetchone().count
        
        await db.commit()
        
        # Broadcast real-time update
        await manager.broadcast_to_activity({
            "type": "comment_like_update",
            "comment_id": comment_id,
            "user_id": user_id,
            "is_liked": is_liked,
            "like_count": like_count
        }, activity_id)
        
        return {
            "message": message,
            "is_liked": is_liked,
            "like_count": like_count
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error liking comment: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to like comment")

@router.post("/{activity_id}/comment/public")
async def comment_on_activity_public(
    activity_id: str,
    comment_data: CommentCreate,
    db: AsyncSession = Depends(get_database_write)
):
    """Comment on an activity (public access - no auth required)."""
    
    try:
        # Use a default guest user for public comments
        guest_user_id = "guest_user"
        
        # Insert comment
        insert_query = text("""
            INSERT INTO activity_comments (activity_id, user_id, content, parent_id)
            VALUES (:activity_id, :user_id, :content, :parent_id)
            RETURNING id, created_at
        """)
        result = await db.execute(insert_query, {
            "activity_id": activity_id,
            "user_id": guest_user_id,
            "content": comment_data.content,
            "parent_id": comment_data.parent_id
        })
        new_comment = result.fetchone()
        
        # Update activity comments count
        count_query = text("""
            UPDATE user_activities 
            SET comments_count = (
                SELECT COUNT(*) FROM activity_comments 
                WHERE activity_id = :activity_id AND is_deleted = FALSE
            )
            WHERE id = :activity_id
            RETURNING comments_count
        """)
        count_result = await db.execute(count_query, {"activity_id": activity_id})
        comments_count = count_result.fetchone().comments_count
        await db.commit()
        
        # Broadcast real-time update
        await manager.broadcast_to_activity({
            "type": "comment_added",
            "activity_id": activity_id,
            "comment": {
                "id": str(new_comment.id),
                "user_id": guest_user_id,
                "user_name": "Guest User",
                "user_avatar": None,
                "content": comment_data.content,
                "parent_id": comment_data.parent_id,
                "created_at": new_comment.created_at.isoformat(),
                "like_count": 0,
                "is_liked": False
            },
            "comments_count": comments_count
        }, activity_id)
        
        return {
            "message": "Comment added successfully",
            "comment_id": str(new_comment.id),
            "created_at": new_comment.created_at.isoformat(),
            "comments_count": comments_count
        }
        
    except Exception as e:
        logger.error(f"Error commenting on activity (public): {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to add comment")