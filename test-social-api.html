<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Social API Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 8px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .activity { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px; }
        button { margin: 5px; padding: 8px 16px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>BetBet Social API Test</h1>
    
    <div class="section">
        <h2>1. Test Public Activity Feed</h2>
        <button onclick="testPublicFeed()">Load Public Activities</button>
        <div id="feedResult"></div>
    </div>
    
    <div class="section">
        <h2>2. Test WebSocket Connection</h2>
        <button onclick="testWebSocket()">Test WebSocket</button>
        <div id="wsResult"></div>
    </div>
    
    <div class="section">
        <h2>3. Test Comment API (requires auth)</h2>
        <button onclick="testCommentAPI()">Test Comments</button>
        <div id="commentResult"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8009';
        
        async function testPublicFeed() {
            const result = document.getElementById('feedResult');
            result.innerHTML = '<p>Loading...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/api/v1/feed/public`);
                const data = await response.json();
                
                if (response.ok) {
                    result.className = 'success';
                    result.innerHTML = `
                        <h3>✅ Public Feed Success</h3>
                        <p>Loaded ${data.length} activities</p>
                        ${data.slice(0, 3).map(activity => `
                            <div class="activity">
                                <strong>${activity.user_name}</strong> - ${activity.type}<br>
                                ${activity.content}<br>
                                <small>Likes: ${activity.likes} | Comments: ${activity.comments} | Shares: ${activity.shares}</small>
                            </div>
                        `).join('')}
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                result.className = 'error';
                result.innerHTML = `<h3>❌ Public Feed Error</h3><p>${error.message}</p>`;
            }
        }
        
        function testWebSocket() {
            const result = document.getElementById('wsResult');
            result.innerHTML = '<p>Connecting...</p>';
            
            const activityId = '95ab0bfd-a2c2-4150-b0ec-d64099e3f287'; // Use a real activity ID from the feed
            const wsUrl = `ws://localhost:8009/api/v1/social/ws/activity/${activityId}`;
            
            const ws = new WebSocket(wsUrl);
            
            ws.onopen = () => {
                result.className = 'success';
                result.innerHTML = '<h3>✅ WebSocket Connected</h3><p>Connection established successfully!</p>';
                ws.send('ping');
            };
            
            ws.onmessage = (event) => {
                const data = event.data;
                result.innerHTML += `<p>Received: ${data}</p>`;
            };
            
            ws.onerror = (error) => {
                result.className = 'error';
                result.innerHTML = `<h3>❌ WebSocket Error</h3><p>${error}</p>`;
            };
            
            ws.onclose = () => {
                result.innerHTML += '<p>Connection closed</p>';
            };
            
            // Close after 5 seconds
            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
            }, 5000);
        }
        
        async function testCommentAPI() {
            const result = document.getElementById('commentResult');
            result.innerHTML = '<p>Testing...</p>';
            
            const activityId = '95ab0bfd-a2c2-4150-b0ec-d64099e3f287';
            
            try {
                const response = await fetch(`${API_BASE}/api/v1/social/${activityId}/comments`);
                
                if (response.status === 401) {
                    result.className = 'success';
                    result.innerHTML = '<h3>✅ Authentication Working</h3><p>Correctly returned 401 Unauthorized for protected endpoint</p>';
                } else if (response.ok) {
                    const data = await response.json();
                    result.className = 'success';
                    result.innerHTML = `<h3>✅ Comments API Success</h3><p>Loaded ${data.length} comments</p>`;
                } else {
                    throw new Error(`Unexpected status: ${response.status}`);
                }
            } catch (error) {
                result.className = 'error';
                result.innerHTML = `<h3>❌ Comments API Error</h3><p>${error.message}</p>`;
            }
        }
    </script>
</body>
</html>